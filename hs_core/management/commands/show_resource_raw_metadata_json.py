import json

from django.core.management.base import BaseCommand, CommandError
from django.core.exceptions import ObjectDoesNotExist

from hs_core.hydroshare.utils import get_resource_by_shortkey


class Command(BaseCommand):
    help = "Show the raw JSON representation of a resource's metadata."

    def add_arguments(self, parser):
        # ID of the resource for which metadata needs to be shown
        parser.add_argument(
            "resource_id",
            type=str,
            help="Required. The existing id (short_id) of the resource to show raw metadata for",
        )
        # Optional flag to include aggregation-level metadata
        parser.add_argument(
            "--include_aggregations",
            action="store_true",
            default=False,
            help="Include aggregation-level metadata for all logical files in the resource",
        )

    def handle(self, *args, **options):
        if not options["resource_id"]:
            raise CommandError("resource_id argument is required")

        res_id = options["resource_id"]

        try:
            resource = get_resource_by_shortkey(res_id, or_404=False)
        except ObjectDoesNotExist:
            raise CommandError("No Resource found for id {}".format(res_id))

        # Get the raw metadata JSON and print it
        print("=== RESOURCE METADATA ===")
        metadata_json = resource.metadata.to_json()
        print(json.dumps(metadata_json, indent=4))

        # If include_aggregations flag is set, also print aggregation-level metadata
        if options["include_aggregations"]:
            print("\n=== AGGREGATION METADATA ===")
            for logical_file in resource.logical_files:
                print(f"\n--- Aggregation Type: {logical_file.get_aggregation_type_name()} ---")
                aggregation_metadata_json = logical_file.metadata.to_json()
                print(json.dumps(aggregation_metadata_json, indent=4))
