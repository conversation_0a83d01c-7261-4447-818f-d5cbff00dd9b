import json
import os
import tempfile
from django.core.management.base import BaseCommand, CommandError
from django.core.exceptions import ObjectDoesNotExist

from hs_core.hydroshare.utils import get_resource_by_shortkey
from hs_core.hydroshare.resource import save_resource_metadata_json
from hs_file_types.enums import AggregationMetaFilePath


class Command(BaseCommand):
    help = "Show the JSON representation of a resource's metadata."

    def add_arguments(self, parser):
        # ID of the resource for which metadata needs to be shown
        parser.add_argument(
            "resource_id",
            type=str,
            help=("Required. The existing id (short_id) of" " the resource to show metadata for"),
        )
        # Optional flag to simulate publishing the resource before showing metadata
        parser.add_argument(
            "--set_published",
            action="store_true",  # Sets the value to True if the flag is present
            default=False,
            help="Simulate setting the resource to published before generating JSON.",
        )

    def handle(self, *args, **options):
        if not options["resource_id"]:
            raise CommandError("resource_id argument is required")
        res_id = options["resource_id"]
        try:
            resource = get_resource_by_shortkey(res_id, or_404=False)
        except ObjectDoesNotExist:
            raise CommandError("No Resource found for id {}".format(res_id))

        if options["set_published"]:
            # simulate a published resource
            # set the resource as published
            resource.set_published(True)
            # if the published date is already there then delete it
            resource.metadata.dates.filter(type='published').delete()

            # create published date
            resource.metadata.create_element('date', type='published', start_date=resource.updated)
        else:
            # set the resource as not published
            resource.set_published(False)
            # if the published date is already there then delete it
            resource.metadata.dates.filter(type='published').delete()

        metadata_json = resource.metadata.to_json()
        print(json.dumps(metadata_json, indent=4))
        
        # print the metadata json for all logical files of the resource
        # print("\nMetadata JSON for all logical files of the resource:")
        # for logical_file in resource.logical_files:
        #     # print the logicla file type name
        #     print(f"\nLogical file type name: {logical_file.get_aggregation_type_name()}")
        #     print(json.dumps(logical_file.metadata.to_json(), indent=4))

        # delete the metadata json file from the storage/bucket
        # storage = resource.get_s3_storage()
        # json_file_name = AggregationMetaFilePath.METADATA_JSON_FILE_NAME
        # json_file_path = os.path.join(resource.file_path, json_file_name)
        # if storage.exists(json_file_path):
        #     storage.delete(json_file_path)
        #     print(f"\nDeleted metadata JSON file '{json_file_name}' from storage.")
        # else:
        #     print(f"\nMetadata JSON file '{json_file_name}' not found in storage.")
        # # alos delete the metadata json file for each logical file
        # for logical_file in resource.logical_files:
        #     json_file_path = logical_file.metadata_json_file_path
        #     if storage.exists(json_file_path):
        #         storage.delete(json_file_path)
        #         print(f"\nDeleted metadata JSON file '{json_file_path}' from storage.")
        #     else:
        #         print(f"\nMetadata JSON file '{json_file_path}' not found in storage.")

        # Save the metadata JSON to a file on the storage/bucket
        save_resource_metadata_json(resource)

        # Create and save a dummy user metadata file for testing at the root
        # storage = resource.get_s3_storage()
        # dummy_meta_filename_root = 'test_aggregation_at_root.txt.hs_user_metadata.json'
        # dummy_meta_content_root = '{"user_metadata": "some random text for testing at root"}'
        # dummy_meta_storage_path_root = os.path.join(resource.file_path, dummy_meta_filename_root)

        # try:
        #     with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_meta_file:
        #         temp_meta_file.write(dummy_meta_content_root)
        #         temp_file_name = temp_meta_file.name
        #     storage.saveFile(temp_file_name, dummy_meta_storage_path_root)
        #     print(f"\nDummy metadata file '{dummy_meta_filename_root}' saved to storage root.")
        #     os.remove(temp_file_name) # Clean up the local temp file
        # except Exception as e:
        #     print(f"\nError saving dummy metadata file to storage root: {e}")

        # # Create and save another dummy user metadata file inside 'test-folder'
        # dummy_meta_filename_folder = 'test_aggregation_in_folder.txt.hs_user_metadata.json'
        # dummy_meta_content_folder = '{"user_metadata": "some random text for testing in folder"}'
        # # Ensure the target folder exists in the path
        # target_folder = 'test-folder'
        # dummy_meta_storage_path_folder = os.path.join(resource.file_path, target_folder, dummy_meta_filename_folder)

        # try:
        #     with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_meta_file:
        #         temp_meta_file.write(dummy_meta_content_folder)
        #         temp_file_name = temp_meta_file.name
        #     # Note: saveFile might implicitly create the directory depending on the storage backend.
        #     # If not, directory creation logic might be needed here.
        #     storage.saveFile(temp_file_name, dummy_meta_storage_path_folder)
        #     print(f"\nDummy metadata file '{dummy_meta_filename_folder}' saved to storage folder '{target_folder}'.")
        #     os.remove(temp_file_name) # Clean up the local temp file
        # except Exception as e:
        #     print(f"\nError saving dummy metadata file to storage folder '{target_folder}': {e}")

        # Check if the metadata JSON file exists in storage
        storage = resource.get_s3_storage()
        json_file_name = AggregationMetaFilePath.METADATA_JSON_FILE_NAME
        json_file_name_endswith = AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        try:
            _, files, _ = storage.listdir(resource.file_path, remove_metadata=False)
            # print the files in the resource
            print("\nFiles in the resource contents directory as listed in storage:")
            for file in files:
                print(file)

            if json_file_name in files:
                print(f"\nMetadata JSON file '{json_file_name}' successfully saved to storage.")
            elif any(file.endswith(json_file_name_endswith) for file in files):
                print(f"\nMetadata JSON file found in storage with name ending in '{json_file_name_endswith}'.")
            else:
                print(f"\nWarning: Metadata JSON file '{json_file_name}' not found in storage listing.")
        except Exception as e:
            print(f"\nError checking storage for metadata JSON file: {e}")
        
        # listdir using the test-folder
        # try:
        #     _, files, _ = storage.listdir(os.path.join(resource.file_path, target_folder), remove_metadata=False)
        #     # print the files in the resource
        #     print(f"\nFiles in the resource {target_folder} directory as listed in storage:")
        #     for file in files:
        #         print(file)
        #     if json_file_name in files:
        #         print(f"\nMetadata JSON file '{json_file_name}' successfully saved to storage.")
        #     elif any(file.endswith(json_file_name_endswith) for file in files):
        #         print(f"\nMetadata JSON file found in storage with name ending in '{json_file_name_endswith}'.")
        #     else:
        #         print(f"\nWarning: Metadata JSON file '{json_file_name}' not found in storage listing.")
        # except Exception as e:
        #     print(f"\nError checking storage for metadata JSON file: {e}")
        
        # listdir using the some folders
        folders_to_list = ('test-folder', 'some-folder')
        for folder_to_list in folders_to_list:
            try:
                _, files, _ = storage.listdir(os.path.join(resource.file_path, folder_to_list), remove_metadata=False)
                # print the files in the resource
                print(f"\nFiles in the resource {folder_to_list} directory as listed in storage:")
                for file in files:
                    print(file)
                if json_file_name in files:
                    print(f"\nMetadata JSON file '{json_file_name}' successfully saved to storage.")
                elif any(file.endswith(json_file_name_endswith) for file in files):
                    print(f"\nMetadata JSON file found in storage with name ending in '{json_file_name_endswith}'.")
                else:
                    print(f"\nWarning: Metadata JSON file '{json_file_name}' not found in storage listing.")
            except Exception as e:
                print(f"\nError checking storage for metadata JSON file: {e}")

        # list the files in the resource
        print("\nFiles in the resource.files():")
        for file in resource.files.all():
            print(file.short_path)
        print("Resource ID: {}".format(resource.short_id))

        # print the identifiers for the creators
        # print("\nIdentifiers for the creators:")
        # for creator in resource.metadata.creators.all():
        #     print(f"Creator: {creator.name}")
        #     print(creator.identifiers)
