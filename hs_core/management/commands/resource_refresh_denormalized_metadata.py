"""
Management command to refresh the denormalized metadata for a specific resource.

This command takes a resource ID as an argument and updates the denormalized_metadata
field using the update_all_denormalized_metadata method from the resource object.
"""

from django.core.management.base import BaseCommand, CommandError
from django.core.exceptions import ObjectDoesNotExist

from hs_core.hydroshare.utils import get_resource_by_shortkey


class Command(BaseCommand):
    help = "Refresh the denormalized metadata for a specific resource using the " \
    "resource's update_all_denormalized_metadata method"

    def add_arguments(self, parser):
        # Required resource ID argument
        parser.add_argument(
            "resource_id",
            type=str,
            help="Required. The existing id (short_id) of the resource to refresh denormalized metadata for"
        )

        # Optional verbose flag
        parser.add_argument(
            "--verbose",
            action="store_true",
            default=False,
            help="Enable verbose output to show detailed information about the update process"
        )

    def handle(self, *args, **options):
        resource_id = options["resource_id"]
        verbose = options.get("verbose", False)
        
        if not resource_id:
            raise CommandError("resource_id argument is required")

        try:
            resource = get_resource_by_shortkey(resource_id, or_404=False)
        except ObjectDoesNotExist:
            raise CommandError(f"No Resource found for id {resource_id}")

        if verbose:
            self.stdout.write(f"Found resource: {resource.metadata.title.value if hasattr(resource.metadata, 'title') and resource.metadata.title else 'Untitled'}")
            self.stdout.write(f"Resource ID: {resource.short_id}")
            self.stdout.write("Current denormalized metadata:")
            self.stdout.write(str(resource.denormalized_metadata))

        try:
            # Call the update_all_denormalized_metadata method
            resource.update_all_denormalized_metadata()

            # Refresh the resource object to get updated denormalized metadata
            resource.refresh_from_db()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully updated denormalized metadata for resource {resource_id}"
                )
            )

            if verbose:
                self.stdout.write("Updated denormalized metadata:")
                self.stdout.write(str(resource.denormalized_metadata))
                
        except Exception as e:
            raise CommandError(f"Failed to update denormalized metadata for resource {resource_id}: {str(e)}")
