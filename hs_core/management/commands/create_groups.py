import random
from django.contrib.auth.models import Group, User
from django.core.management.base import BaseCommand

from hs_access_control.models import PrivilegeCodes
from hs_core.hydroshare import create_account


class Command(BaseCommand):
    help = "Create 100 group for testing."

    def handle(self, *args, **options):
        self.group, _ = Group.objects.get_or_create(name="Hydroshare Author")
        self.pabitra = User.objects.filter(email="<EMAIL>").first()
        # for i in range(1, 101):
        #     group = self.pabitra.uaccess.create_group(
        #         title=f'Test Group-{i}',
        #         description=f"Group-{i} for testing group listing page loading performance",
        #         purpose="To test group functionality")
        #
        #     print(f">> created group:{group.name}")

        # add 100 users to one group
        group = Group.objects.filter(name="Test Group-10").first()
        assert group is not None
        for i in range(1, 100):
            group_member = create_account(
                email=f"kelly-{i}@gmail.com",
                username=f"kelly-{i}",
                first_name=f"Kelly-{i}",
                last_name=f"Miller-{i}",
                superuser=False,
                groups=[],
            )
            self.pabitra.uaccess.share_group_with_user(
                group, group_member, PrivilegeCodes.VIEW
            )
            print(f">> added group member:{group_member.email} to group:{group.name}")
