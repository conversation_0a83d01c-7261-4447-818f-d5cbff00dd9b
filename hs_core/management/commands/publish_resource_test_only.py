from django.contrib.auth.models import User
from django.core.management.base import BaseCommand, CommandError
from django.core.exceptions import ObjectDoesNotExist

from hs_core.hydroshare.utils import get_resource_by_shortkey
from hs_core.hydroshare import publish_resource


class Command(BaseCommand):
    help = "Publish a resource - TEST ONLY"

    def add_arguments(self, parser):
        # ID of a published resource for which crossref metadata needs to be updated
        parser.add_argument(
            "resource_id",
            type=str,
            help=("Required. The existing id (short_id) of" " the resource to publish"),
        )

    def handle(self, *args, **options):
        if not options["resource_id"]:
            raise CommandError("resource_id argument is required")
        res_id = options["resource_id"]
        try:
            resource = get_resource_by_shortkey(res_id, or_404=False)
        except ObjectDoesNotExist:
            raise CommandError("No Resource found for id {}".format(res_id))
        if resource.raccess.published:
            raise CommandError("Resource is already published")
        hs_admin_user = User.objects.filter(first_name="HydroShare").first()
        publish_resource(user=hs_admin_user, pk=resource.short_id)
        print(
            f"Successfully published resource with id {resource.short_id}. DOI: {resource.doi}"
        )
