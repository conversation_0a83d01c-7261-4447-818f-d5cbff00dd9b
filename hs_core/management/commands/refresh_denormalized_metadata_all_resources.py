"""
Management command to refresh the denormalized metadata for all resources.

This command iterates through all resources and updates their denormalized_metadata
field using the update_all_denormalized_metadata method from each resource object.
It tracks failed resources and their errors, and prints success information for 
each successfully updated resource.
"""

from django.core.management.base import BaseCommand

from hs_core.models import BaseResource


class Command(BaseCommand):
    help = "Refresh the denormalized metadata for all resources using each resource's update_all_denormalized_metadata method"

    def add_arguments(self, parser):
        # Optional verbose flag
        parser.add_argument(
            "--verbose",
            action="store_true",
            default=False,
            help="Enable verbose output to show detailed information about the update process"
        )

        # Optional limit flag for testing purposes
        parser.add_argument(
            "--limit",
            type=int,
            help="Optional. Limit the number of resources to process (useful for testing)"
        )

    def handle(self, *args, **options):
        verbose = options.get("verbose", False)
        limit = options.get("limit")

        # Get all resources
        resources = BaseResource.objects.all()

        if limit:
            resources = resources[:limit]
            self.stdout.write(f"Processing first {limit} resources...")

        total_resources = resources.count()
        self.stdout.write(f"Found {total_resources} resources to process")

        # Track failed resources and their errors
        failed_resources = {}
        success_count = 0

        for index, base_resource in enumerate(resources, 1):
            try:
                # Get the concrete resource instance
                resource = base_resource.get_content_model()

                if verbose:
                    resource_title = resource.metadata.title.value if hasattr(resource.metadata, 'title') and resource.metadata.title else 'Untitled'
                    self.stdout.write(f"[{index}/{total_resources}] Processing resource: {resource_title} (ID: {resource.short_id})")
                    self.stdout.write(f"  Current denormalized metadata: {resource.denormalized_metadata}")

                # Call the update_all_denormalized_metadata method
                resource.update_all_denormalized_metadata()

                # Refresh the resource object to get updated denormalized metadata
                resource.refresh_from_db()

                success_count += 1

                # Print updated metadata for each successful resource
                self.stdout.write(
                    self.style.SUCCESS(f"[{index}/{total_resources}] Successfully updated resource {resource.short_id}")
                )
                self.stdout.write(f"  Updated denormalized metadata: {resource.denormalized_metadata}")

                if verbose:
                    self.stdout.write("")  # Empty line for better readability

            except Exception as e:
                # Track the failed resource and its error
                failed_resources[base_resource.short_id] = str(e)
                self.stdout.write(
                    self.style.ERROR(f"[{index}/{total_resources}] Failed to update resource {base_resource.short_id}: {str(e)}")
                )

        # Print summary
        self.stdout.write("\n" + "=" * 60)
        self.stdout.write("SUMMARY")
        self.stdout.write("=" * 60)
        self.stdout.write(f"Total resources processed: {total_resources}")
        self.stdout.write(self.style.SUCCESS(f"Successfully updated: {success_count}"))
        self.stdout.write(self.style.ERROR(f"Failed to update: {len(failed_resources)}"))

        # Print details of failed resources
        if failed_resources:
            self.stdout.write("\n" + "=" * 60)
            self.stdout.write("FAILED RESOURCES AND ERRORS")
            self.stdout.write("=" * 60)
            for resource_id, error in failed_resources.items():
                self.stdout.write(f"Resource ID: {resource_id}")
                self.stdout.write(f"Error: {error}")
                self.stdout.write("-" * 40)
        else:
            self.stdout.write(self.style.SUCCESS("\nAll resources were updated successfully!"))
