import logging

from django.core.management.base import BaseCommand
from django.db.models import Q

from hs_core.hydroshare import utils
from hs_core.models import BaseResource


class Command(BaseCommand):
    help = (
        "Reports if the resource specified as isVersionOf or isReplacedBy as part of the relation metadata exists "
        "for all resources."
    )

    def handle(self, *args, **options):
        log = logging.getLogger()
        res_count = BaseResource.objects.count()
        print("Total resources:{}".format(res_count))
        res_count = 0
        for res in BaseResource.objects.all().iterator():
            res = res.get_content_model()
            if res.metadata is None:
                log_msg = "Resource with ID:{} missing metadata object. Skipping this resource".format(
                    res.short_id
                )
                print(log_msg)
                log.warning(log_msg)
                continue
            res_count += 1
            for rel in res.metadata.relations.filter(
                Q(type="isVersionOf") | Q(type="isReplacedBy")
            ).all():
                res_url_parts = rel.value.split("/resource/")
                if len(res_url_parts) == 2:
                    res_id = res_url_parts[1]
                    try:
                        utils.get_resource_by_shortkey(shortkey=res_id, or_404=False)
                    except BaseResource.DoesNotExist:
                        log_msg = (
                            "Resource doesn't exist for ID:{} as found for relation type '{}' "
                            "for resource with ID:{}"
                        )
                        log_msg = log_msg.format(res_id, rel.type, res.short_id)
                        print(log_msg)
                        log.warning(log_msg)
                else:
                    log_msg = "Invalid value ({}) for relation type '{}' for resource with ID:{}."
                    log_msg = log_msg.format(rel.value, rel.type, res.short_id)
                    print(log_msg)
                    log.warning(log_msg)
        print("Total resources processed:{}".format(res_count))
