# -*- coding: utf-8 -*-
# Generated by Django 1.11.18 on 2019-07-11 14:40


from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Topic',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='TopicEntry',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.IntegerField(help_text=b'Position of this entry: 1-n')),
                ('topic', models.ForeignKey(help_text=b'One topic entry for a resource', on_delete=django.db.models.deletion.CASCADE, to='hs_communities.Topic')),
            ],
        ),
        migrations.CreateModel(
            name='Topics',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('topics', models.ManyToManyField(help_text=b'A list of topics, in order', null=True, to='hs_communities.TopicEntry')),
            ],
        ),
    ]
