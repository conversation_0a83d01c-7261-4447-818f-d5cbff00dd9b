(function(t,e){"object"===typeof exports&&"object"===typeof module?module.exports=e():"function"===typeof define&&define.amd?define([],e):"object"===typeof exports?exports["VueBootstrapTypeahead"]=e():t["VueBootstrapTypeahead"]=e()})("undefined"!==typeof self?self:this,function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s="fb15")}({"0bfb":function(t,e,n){"use strict";var r=n("cb7c");t.exports=function(){var t=r(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},"0d58":function(t,e,n){var r=n("ce10"),i=n("e11e");t.exports=Object.keys||function(t){return r(t,i)}},"11e9":function(t,e,n){var r=n("52a7"),i=n("4630"),o=n("6821"),a=n("6a99"),u=n("69a8"),c=n("c69a"),s=Object.getOwnPropertyDescriptor;e.f=n("9e1e")?s:function(t,e){if(t=o(t),e=a(e,!0),c)try{return s(t,e)}catch(t){}if(u(t,e))return i(!r.f.call(t,e),t[e])}},1495:function(t,e,n){var r=n("86cc"),i=n("cb7c"),o=n("0d58");t.exports=n("9e1e")?Object.defineProperties:function(t,e){i(t);var n,a=o(e),u=a.length,c=0;while(u>c)r.f(t,n=a[c++],e[n]);return t}},"1eb2":function(t,e,n){var r;"undefined"!==typeof window&&((r=window.document.currentScript)&&(r=r.src.match(/(.+\/)[^/]+\.js$/))&&(n.p=r[1]))},"214f":function(t,e,n){"use strict";var r=n("32e9"),i=n("2aba"),o=n("79e5"),a=n("be13"),u=n("2b4c");t.exports=function(t,e,n){var c=u(t),s=n(a,c,""[t]),f=s[0],p=s[1];o(function(){var e={};return e[c]=function(){return 7},7!=""[t](e)})&&(i(String.prototype,t,f),r(RegExp.prototype,c,2==e?function(t,e){return p.call(t,this,e)}:function(t){return p.call(t,this)}))}},"230e":function(t,e,n){var r=n("d3f4"),i=n("7726").document,o=r(i)&&r(i.createElement);t.exports=function(t){return o?i.createElement(t):{}}},"2aba":function(t,e,n){var r=n("7726"),i=n("32e9"),o=n("69a8"),a=n("ca5a")("src"),u="toString",c=Function[u],s=(""+c).split(u);n("8378").inspectSource=function(t){return c.call(t)},(t.exports=function(t,e,n,u){var c="function"==typeof n;c&&(o(n,"name")||i(n,"name",e)),t[e]!==n&&(c&&(o(n,a)||i(n,a,t[e]?""+t[e]:s.join(String(e)))),t===r?t[e]=n:u?t[e]?t[e]=n:i(t,e,n):(delete t[e],i(t,e,n)))})(Function.prototype,u,function(){return"function"==typeof this&&this[a]||c.call(this)})},"2aeb":function(t,e,n){var r=n("cb7c"),i=n("1495"),o=n("e11e"),a=n("613b")("IE_PROTO"),u=function(){},c="prototype",s=function(){var t,e=n("230e")("iframe"),r=o.length,i="<",a=">";e.style.display="none",n("fab2").appendChild(e),e.src="javascript:",t=e.contentWindow.document,t.open(),t.write(i+"script"+a+"document.F=Object"+i+"/script"+a),t.close(),s=t.F;while(r--)delete s[c][o[r]];return s()};t.exports=Object.create||function(t,e){var n;return null!==t?(u[c]=r(t),n=new u,u[c]=null,n[a]=t):n=s(),void 0===e?n:i(n,e)}},"2b4c":function(t,e,n){var r=n("5537")("wks"),i=n("ca5a"),o=n("7726").Symbol,a="function"==typeof o,u=t.exports=function(t){return r[t]||(r[t]=a&&o[t]||(a?o:i)("Symbol."+t))};u.store=r},"2d00":function(t,e){t.exports=!1},"2d95":function(t,e){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},"2f21":function(t,e,n){"use strict";var r=n("79e5");t.exports=function(t,e){return!!t&&r(function(){e?t.call(null,function(){},1):t.call(null)})}},"32e9":function(t,e,n){var r=n("86cc"),i=n("4630");t.exports=n("9e1e")?function(t,e,n){return r.f(t,e,i(1,n))}:function(t,e,n){return t[e]=n,t}},"3b2b":function(t,e,n){var r=n("7726"),i=n("5dbc"),o=n("86cc").f,a=n("9093").f,u=n("aae3"),c=n("0bfb"),s=r.RegExp,f=s,p=s.prototype,l=/a/g,d=/a/g,h=new s(l)!==l;if(n("9e1e")&&(!h||n("79e5")(function(){return d[n("2b4c")("match")]=!1,s(l)!=l||s(d)==d||"/a/i"!=s(l,"i")}))){s=function(t,e){var n=this instanceof s,r=u(t),o=void 0===e;return!n&&r&&t.constructor===s&&o?t:i(h?new f(r&&!o?t.source:t,e):f((r=t instanceof s)?t.source:t,r&&o?c.call(t):e),n?this:p,s)};for(var v=function(t){t in s||o(s,t,{configurable:!0,get:function(){return f[t]},set:function(e){f[t]=e}})},b=a(f),y=0;b.length>y;)v(b[y++]);p.constructor=s,s.prototype=p,n("2aba")(r,"RegExp",s)}n("7a56")("RegExp")},4588:function(t,e){var n=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?r:n)(t)}},4630:function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},4917:function(t,e,n){n("214f")("match",1,function(t,e,n){return[function(n){"use strict";var r=t(this),i=void 0==n?void 0:n[e];return void 0!==i?i.call(n,r):new RegExp(n)[e](String(r))},n]})},"4bf8":function(t,e,n){var r=n("be13");t.exports=function(t){return Object(r(t))}},"52a7":function(t,e){e.f={}.propertyIsEnumerable},5537:function(t,e,n){var r=n("8378"),i=n("7726"),o="__core-js_shared__",a=i[o]||(i[o]={});(t.exports=function(t,e){return a[t]||(a[t]=void 0!==e?e:{})})("versions",[]).push({version:r.version,mode:n("2d00")?"pure":"global",copyright:"© 2018 Denis Pushkarev (zloirock.ru)"})},"55dd":function(t,e,n){"use strict";var r=n("5ca1"),i=n("d8e8"),o=n("4bf8"),a=n("79e5"),u=[].sort,c=[1,2,3];r(r.P+r.F*(a(function(){c.sort(void 0)})||!a(function(){c.sort(null)})||!n("2f21")(u)),"Array",{sort:function(t){return void 0===t?u.call(o(this)):u.call(o(this),i(t))}})},"5ca1":function(t,e,n){var r=n("7726"),i=n("8378"),o=n("32e9"),a=n("2aba"),u=n("9b43"),c="prototype",s=function(t,e,n){var f,p,l,d,h=t&s.F,v=t&s.G,b=t&s.S,y=t&s.P,_=t&s.B,g=v?r:b?r[e]||(r[e]={}):(r[e]||{})[c],m=v?i:i[e]||(i[e]={}),x=m[c]||(m[c]={});for(f in v&&(n=e),n)p=!h&&g&&void 0!==g[f],l=(p?g:n)[f],d=_&&p?u(l,r):y&&"function"==typeof l?u(Function.call,l):l,g&&a(g,f,l,t&s.U),m[f]!=l&&o(m,f,d),y&&x[f]!=l&&(x[f]=l)};r.core=i,s.F=1,s.G=2,s.S=4,s.P=8,s.B=16,s.W=32,s.U=64,s.R=128,t.exports=s},"5dbc":function(t,e,n){var r=n("d3f4"),i=n("8b97").set;t.exports=function(t,e,n){var o,a=e.constructor;return a!==n&&"function"==typeof a&&(o=a.prototype)!==n.prototype&&r(o)&&i&&i(t,o),t}},"613b":function(t,e,n){var r=n("5537")("keys"),i=n("ca5a");t.exports=function(t){return r[t]||(r[t]=i(t))}},"626a":function(t,e,n){var r=n("2d95");t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==r(t)?t.split(""):Object(t)}},6821:function(t,e,n){var r=n("626a"),i=n("be13");t.exports=function(t){return r(i(t))}},"69a8":function(t,e){var n={}.hasOwnProperty;t.exports=function(t,e){return n.call(t,e)}},"6a99":function(t,e,n){var r=n("d3f4");t.exports=function(t,e){if(!r(t))return t;var n,i;if(e&&"function"==typeof(n=t.toString)&&!r(i=n.call(t)))return i;if("function"==typeof(n=t.valueOf)&&!r(i=n.call(t)))return i;if(!e&&"function"==typeof(n=t.toString)&&!r(i=n.call(t)))return i;throw TypeError("Can't convert object to primitive value")}},"6dd8":function(t,e,n){"use strict";(function(t){var n=function(){if("undefined"!==typeof Map)return Map;function t(t,e){var n=-1;return t.some(function(t,r){return t[0]===e&&(n=r,!0)}),n}return function(){function e(){this.__entries__=[]}var n={size:{configurable:!0}};return n.size.get=function(){return this.__entries__.length},e.prototype.get=function(e){var n=t(this.__entries__,e),r=this.__entries__[n];return r&&r[1]},e.prototype.set=function(e,n){var r=t(this.__entries__,e);~r?this.__entries__[r][1]=n:this.__entries__.push([e,n])},e.prototype.delete=function(e){var n=this.__entries__,r=t(n,e);~r&&n.splice(r,1)},e.prototype.has=function(e){return!!~t(this.__entries__,e)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(t,e){var n=this;void 0===e&&(e=null);for(var r=0,i=n.__entries__;r<i.length;r+=1){var o=i[r];t.call(e,o[1],o[0])}},Object.defineProperties(e.prototype,n),e}()}(),r="undefined"!==typeof window&&"undefined"!==typeof document&&window.document===document,i=function(){return"undefined"!==typeof t&&t.Math===Math?t:"undefined"!==typeof self&&self.Math===Math?self:"undefined"!==typeof window&&window.Math===Math?window:Function("return this")()}(),o=function(){return"function"===typeof requestAnimationFrame?requestAnimationFrame.bind(i):function(t){return setTimeout(function(){return t(Date.now())},1e3/60)}}(),a=2,u=function(t,e){var n=!1,r=!1,i=0;function u(){n&&(n=!1,t()),r&&s()}function c(){o(u)}function s(){var t=Date.now();if(n){if(t-i<a)return;r=!0}else n=!0,r=!1,setTimeout(c,e);i=t}return s},c=20,s=["top","right","bottom","left","width","height","size","weight"],f="undefined"!==typeof MutationObserver,p=function(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=u(this.refresh.bind(this),c)};p.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},p.prototype.removeObserver=function(t){var e=this.observers_,n=e.indexOf(t);~n&&e.splice(n,1),!e.length&&this.connected_&&this.disconnect_()},p.prototype.refresh=function(){var t=this.updateObservers_();t&&this.refresh()},p.prototype.updateObservers_=function(){var t=this.observers_.filter(function(t){return t.gatherActive(),t.hasActive()});return t.forEach(function(t){return t.broadcastActive()}),t.length>0},p.prototype.connect_=function(){r&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),f?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},p.prototype.disconnect_=function(){r&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},p.prototype.onTransitionEnd_=function(t){var e=t.propertyName;void 0===e&&(e="");var n=s.some(function(t){return!!~e.indexOf(t)});n&&this.refresh()},p.getInstance=function(){return this.instance_||(this.instance_=new p),this.instance_},p.instance_=null;var l=function(t,e){for(var n=0,r=Object.keys(e);n<r.length;n+=1){var i=r[n];Object.defineProperty(t,i,{value:e[i],enumerable:!1,writable:!1,configurable:!0})}return t},d=function(t){var e=t&&t.ownerDocument&&t.ownerDocument.defaultView;return e||i},h=E(0,0,0,0);function v(t){return parseFloat(t)||0}function b(t){var e=[],n=arguments.length-1;while(n-- >0)e[n]=arguments[n+1];return e.reduce(function(e,n){var r=t["border-"+n+"-width"];return e+v(r)},0)}function y(t){for(var e=["top","right","bottom","left"],n={},r=0,i=e;r<i.length;r+=1){var o=i[r],a=t["padding-"+o];n[o]=v(a)}return n}function _(t){var e=t.getBBox();return E(0,0,e.width,e.height)}function g(t){var e=t.clientWidth,n=t.clientHeight;if(!e&&!n)return h;var r=d(t).getComputedStyle(t),i=y(r),o=i.left+i.right,a=i.top+i.bottom,u=v(r.width),c=v(r.height);if("border-box"===r.boxSizing&&(Math.round(u+o)!==e&&(u-=b(r,"left","right")+o),Math.round(c+a)!==n&&(c-=b(r,"top","bottom")+a)),!x(t)){var s=Math.round(u+o)-e,f=Math.round(c+a)-n;1!==Math.abs(s)&&(u-=s),1!==Math.abs(f)&&(c-=f)}return E(i.left,i.top,u,c)}var m=function(){return"undefined"!==typeof SVGGraphicsElement?function(t){return t instanceof d(t).SVGGraphicsElement}:function(t){return t instanceof d(t).SVGElement&&"function"===typeof t.getBBox}}();function x(t){return t===d(t).document.documentElement}function w(t){return r?m(t)?_(t):g(t):h}function O(t){var e=t.x,n=t.y,r=t.width,i=t.height,o="undefined"!==typeof DOMRectReadOnly?DOMRectReadOnly:Object,a=Object.create(o.prototype);return l(a,{x:e,y:n,width:r,height:i,top:n,right:e+r,bottom:i+n,left:e}),a}function E(t,e,n,r){return{x:t,y:e,width:n,height:r}}var S=function(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=E(0,0,0,0),this.target=t};S.prototype.isActive=function(){var t=w(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},S.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t};var T=function(t,e){var n=O(e);l(this,{target:t,contentRect:n})},M=function(t,e,r){if(this.activeObservations_=[],this.observations_=new n,"function"!==typeof t)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=e,this.callbackCtx_=r};M.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!==typeof Element&&Element instanceof Object){if(!(t instanceof d(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)||(e.set(t,new S(t)),this.controller_.addObserver(this),this.controller_.refresh())}},M.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!==typeof Element&&Element instanceof Object){if(!(t instanceof d(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)&&(e.delete(t),e.size||this.controller_.removeObserver(this))}},M.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},M.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(e){e.isActive()&&t.activeObservations_.push(e)})},M.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,e=this.activeObservations_.map(function(t){return new T(t.target,t.broadcastRect())});this.callback_.call(t,e,t),this.clearActive()}},M.prototype.clearActive=function(){this.activeObservations_.splice(0)},M.prototype.hasActive=function(){return this.activeObservations_.length>0};var j="undefined"!==typeof WeakMap?new WeakMap:new n,C=function(t){if(!(this instanceof C))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var e=p.getInstance(),n=new M(t,e,this);j.set(this,n)};["observe","unobserve","disconnect"].forEach(function(t){C.prototype[t]=function(){return(e=j.get(this))[t].apply(e,arguments);var e}});var V=function(){return"undefined"!==typeof i.ResizeObserver?i.ResizeObserver:C}();e["a"]=V}).call(this,n("c8ba"))},7726:function(t,e){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},"77f1":function(t,e,n){var r=n("4588"),i=Math.max,o=Math.min;t.exports=function(t,e){return t=r(t),t<0?i(t+e,0):o(t,e)}},"79e5":function(t,e){t.exports=function(t){try{return!!t()}catch(t){return!0}}},"7a56":function(t,e,n){"use strict";var r=n("7726"),i=n("86cc"),o=n("9e1e"),a=n("2b4c")("species");t.exports=function(t){var e=r[t];o&&e&&!e[a]&&i.f(e,a,{configurable:!0,get:function(){return this}})}},8378:function(t,e){var n=t.exports={version:"2.5.7"};"number"==typeof __e&&(__e=n)},"86cc":function(t,e,n){var r=n("cb7c"),i=n("c69a"),o=n("6a99"),a=Object.defineProperty;e.f=n("9e1e")?Object.defineProperty:function(t,e,n){if(r(t),e=o(e,!0),r(n),i)try{return a(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(t[e]=n.value),t}},"892a":function(t,e,n){"use strict";var r=n("9bf9"),i=n.n(r);i.a},"8b97":function(t,e,n){var r=n("d3f4"),i=n("cb7c"),o=function(t,e){if(i(t),!r(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,r){try{r=n("9b43")(Function.call,n("11e9").f(Object.prototype,"__proto__").set,2),r(t,[]),e=!(t instanceof Array)}catch(t){e=!0}return function(t,n){return o(t,n),e?t.__proto__=n:r(t,n),t}}({},!1):void 0),check:o}},9093:function(t,e,n){var r=n("ce10"),i=n("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return r(t,i)}},"9b43":function(t,e,n){var r=n("d8e8");t.exports=function(t,e,n){if(r(t),void 0===e)return t;switch(n){case 1:return function(n){return t.call(e,n)};case 2:return function(n,r){return t.call(e,n,r)};case 3:return function(n,r,i){return t.call(e,n,r,i)}}return function(){return t.apply(e,arguments)}}},"9bf9":function(t,e,n){},"9def":function(t,e,n){var r=n("4588"),i=Math.min;t.exports=function(t){return t>0?i(r(t),9007199254740991):0}},"9e1e":function(t,e,n){t.exports=!n("79e5")(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},a481:function(t,e,n){n("214f")("replace",2,function(t,e,n){return[function(r,i){"use strict";var o=t(this),a=void 0==r?void 0:r[e];return void 0!==a?a.call(r,o,i):n.call(String(o),r,i)},n]})},aa77:function(t,e,n){var r=n("5ca1"),i=n("be13"),o=n("79e5"),a=n("fdef"),u="["+a+"]",c="​",s=RegExp("^"+u+u+"*"),f=RegExp(u+u+"*$"),p=function(t,e,n){var i={},u=o(function(){return!!a[t]()||c[t]()!=c}),s=i[t]=u?e(l):a[t];n&&(i[n]=s),r(r.P+r.F*u,"String",i)},l=p.trim=function(t,e){return t=String(i(t)),1&e&&(t=t.replace(s,"")),2&e&&(t=t.replace(f,"")),t};t.exports=p},aae3:function(t,e,n){var r=n("d3f4"),i=n("2d95"),o=n("2b4c")("match");t.exports=function(t){var e;return r(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==i(t))}},be13:function(t,e){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on  "+t);return t}},c366:function(t,e,n){var r=n("6821"),i=n("9def"),o=n("77f1");t.exports=function(t){return function(e,n,a){var u,c=r(e),s=i(c.length),f=o(a,s);if(t&&n!=n){while(s>f)if(u=c[f++],u!=u)return!0}else for(;s>f;f++)if((t||f in c)&&c[f]===n)return t||f||0;return!t&&-1}}},c5f6:function(t,e,n){"use strict";var r=n("7726"),i=n("69a8"),o=n("2d95"),a=n("5dbc"),u=n("6a99"),c=n("79e5"),s=n("9093").f,f=n("11e9").f,p=n("86cc").f,l=n("aa77").trim,d="Number",h=r[d],v=h,b=h.prototype,y=o(n("2aeb")(b))==d,_="trim"in String.prototype,g=function(t){var e=u(t,!1);if("string"==typeof e&&e.length>2){e=_?e.trim():l(e,3);var n,r,i,o=e.charCodeAt(0);if(43===o||45===o){if(n=e.charCodeAt(2),88===n||120===n)return NaN}else if(48===o){switch(e.charCodeAt(1)){case 66:case 98:r=2,i=49;break;case 79:case 111:r=8,i=55;break;default:return+e}for(var a,c=e.slice(2),s=0,f=c.length;s<f;s++)if(a=c.charCodeAt(s),a<48||a>i)return NaN;return parseInt(c,r)}}return+e};if(!h(" 0o1")||!h("0b1")||h("+0x1")){h=function(t){var e=arguments.length<1?0:t,n=this;return n instanceof h&&(y?c(function(){b.valueOf.call(n)}):o(n)!=d)?a(new v(g(e)),n,h):g(e)};for(var m,x=n("9e1e")?s(v):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),w=0;x.length>w;w++)i(v,m=x[w])&&!i(h,m)&&p(h,m,f(v,m));h.prototype=b,b.constructor=h,n("2aba")(r,d,h)}},c69a:function(t,e,n){t.exports=!n("9e1e")&&!n("79e5")(function(){return 7!=Object.defineProperty(n("230e")("div"),"a",{get:function(){return 7}}).a})},c8ba:function(t,e){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(t){"object"===typeof window&&(n=window)}t.exports=n},ca5a:function(t,e){var n=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+r).toString(36))}},cb7c:function(t,e,n){var r=n("d3f4");t.exports=function(t){if(!r(t))throw TypeError(t+" is not an object!");return t}},ce10:function(t,e,n){var r=n("69a8"),i=n("6821"),o=n("c366")(!1),a=n("613b")("IE_PROTO");t.exports=function(t,e){var n,u=i(t),c=0,s=[];for(n in u)n!=a&&r(u,n)&&s.push(n);while(e.length>c)r(u,n=e[c++])&&(~o(s,n)||s.push(n));return s}},d3f4:function(t,e){t.exports=function(t){return"object"===typeof t?null!==t:"function"===typeof t}},d8e8:function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},e11e:function(t,e){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},fab2:function(t,e,n){var r=n("7726").document;t.exports=r&&r.documentElement},fb15:function(t,e,n){"use strict";n.r(e);n("1eb2");var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{class:t.sizeClasses},[t.$slots.prepend||t.prepend?n("div",{ref:"prependDiv",staticClass:"input-group-prepend"},[t._t("prepend",[n("span",{staticClass:"input-group-text"},[t._v(t._s(t.prepend))])])],2):t._e(),n("input",{ref:"input",class:"form-control "+t.inputClass,attrs:{type:"search",placeholder:t.placeholder,"aria-label":t.placeholder,autocomplete:"off"},domProps:{value:t.inputValue},on:{focus:function(e){t.isFocused=!0},blur:t.handleBlur,input:function(e){t.handleInput(e.target.value)}}}),t.$slots.append||t.append?n("div",{staticClass:"input-group-append"},[t._t("append",[n("span",{staticClass:"input-group-text"},[t._v(t._s(t.append))])])],2):t._e()]),n("vue-bootstrap-typeahead-list",{directives:[{name:"show",rawName:"v-show",value:t.isFocused&&t.data.length>0,expression:"isFocused && data.length > 0"}],ref:"list",staticClass:"vbt-autcomplete-list",attrs:{query:t.inputValue,data:t.formattedData,"background-variant":t.backgroundVariant,"text-variant":t.textVariant,maxMatches:t.maxMatches,minMatchingChars:t.minMatchingChars},on:{hit:t.handleHit},scopedSlots:t._u([t._l(t.$scopedSlots,function(e,n){return{key:n,fn:function(e){var r=e.data,i=e.htmlText;return[t._t(n,null,null,{data:r,htmlText:i})]}}})])})],1)},i=[],o=(n("c5f6"),function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"list-group shadow"},t._l(t.matchedItems,function(e,r){return n("vue-bootstrap-typeahead-list-item",{key:r,attrs:{data:e.data,"html-text":t.highlight(e.text),"background-variant":t.backgroundVariant,"text-variant":t.textVariant},nativeOn:{click:function(n){t.handleHit(e,n)}},scopedSlots:t._u([{key:"suggestion",fn:function(e){var n=e.data,r=e.htmlText;return t.$scopedSlots.suggestion?[t._t("suggestion",null,null,{data:n,htmlText:r})]:void 0}}])})}))}),a=[],u=(n("4917"),n("55dd"),n("3b2b"),n("a481"),function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("a",{class:t.textClasses,attrs:{tabindex:"0",href:"#"},on:{mouseover:function(e){t.active=!0},mouseout:function(e){t.active=!1}}},[t._t("suggestion",[n("span",{domProps:{innerHTML:t._s(t.htmlText)}})],null,{data:t.data,htmlText:t.htmlText})],2)}),c=[],s={name:"VueBootstrapTypeaheadListItem",props:{data:{},htmlText:{type:String},backgroundVariant:{type:String},textVariant:{type:String}},data:function(){return{active:!1}},computed:{textClasses:function(){var t="";return t+=this.active?"active":"",t+=this.backgroundVariant?" bg-".concat(this.backgroundVariant):"",t+=this.textVariant?" text-".concat(this.textVariant):"","vbst-item list-group-item list-group-item-action ".concat(t)}}},f=s;function p(t,e,n,r,i,o,a,u){var c,s="function"===typeof t?t.options:t;if(e&&(s.render=e,s.staticRenderFns=n,s._compiled=!0),r&&(s.functional=!0),o&&(s._scopeId="data-v-"+o),a?(c=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),i&&i.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(a)},s._ssrRegister=c):i&&(c=u?function(){i.call(this,this.$root.$options.shadowRoot)}:i),c)if(s.functional){s._injectStyles=c;var f=s.render;s.render=function(t,e){return c.call(e),f(t,e)}}else{var p=s.beforeCreate;s.beforeCreate=p?[].concat(p,c):[c]}return{exports:t,options:s}}var l=p(f,u,c,!1,null,null,null);l.options.__file="VueBootstrapTypeaheadListItem.vue";var d=l.exports;function h(t){return t.replace(/</g,"&lt;").replace(/>/g,"&gt;")}function v(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}var b={name:"VueBootstrapTypeaheadList",components:{VueBootstrapTypeaheadListItem:d},props:{data:{type:Array,required:!0,validator:function(t){return t instanceof Array}},query:{type:String,default:""},backgroundVariant:{type:String},textVariant:{type:String},maxMatches:{type:Number,default:10},minMatchingChars:{type:Number,default:2}},computed:{highlight:function(){var t=this;return function(e){if(e=h(e),0===t.query.length)return e;var n=new RegExp(t.escapedQuery,"gi");return e.replace(n,"<strong>$&</strong>")}},escapedQuery:function(){return v(h(this.query))},matchedItems:function(){if(0===this.query.length||this.query.length<this.minMatchingChars)return[];var t=new RegExp(this.escapedQuery,"gi");return this.data.filter(function(e){return null!==e.text.match(t)}).sort(function(e,n){var r=e.text.indexOf(e.text.match(t)[0]),i=n.text.indexOf(n.text.match(t)[0]);return r<i?-1:r>i?1:0}).slice(0,this.maxMatches)}},methods:{handleHit:function(t,e){this.$emit("hit",t),e.preventDefault()}}},y=b,_=p(y,o,a,!1,null,null,null);_.options.__file="VueBootstrapTypeaheadList.vue";var g=_.exports,m=n("6dd8"),x={name:"VueBootstrapTypehead",components:{VueBootstrapTypeaheadList:g},props:{size:{type:String,default:null,validator:function(t){return["lg","sm"].indexOf(t)>-1}},value:{type:String},data:{type:Array,required:!0,validator:function(t){return t instanceof Array}},serializer:{type:Function,default:function(t){return t},validator:function(t){return t instanceof Function}},backgroundVariant:String,textVariant:String,inputClass:{type:String,default:""},maxMatches:{type:Number,default:10},minMatchingChars:{type:Number,default:2},placeholder:String,prepend:String,append:String},computed:{sizeClasses:function(){return this.size?"input-group input-group-".concat(this.size):"input-group"},formattedData:function(){var t=this;return this.data instanceof Array?this.data.map(function(e,n){return{id:n,data:e,text:t.serializer(e)}}):[]}},methods:{resizeList:function(t){var e=t.getBoundingClientRect(),n=this.$refs.list.$el.style;if(n.width=e.width+"px",this.$refs.prependDiv){var r=this.$refs.prependDiv.getBoundingClientRect();n.marginLeft=r.width+"px"}},handleHit:function(t){"undefined"!==typeof this.value&&this.$emit("input",t.text),this.inputValue=t.text,this.$emit("hit",t.data),this.$refs.input.blur(),this.isFocused=!1},handleBlur:function(t){var e=t.relatedTarget;e&&e.classList.contains("vbst-item")||(this.isFocused=!1)},handleInput:function(t){this.inputValue=t,"undefined"!==typeof this.value&&this.$emit("input",t)}},data:function(){return{isFocused:!1,inputValue:""}},mounted:function(){var t=this;this.$_ro=new m["a"](function(e){t.resizeList(t.$refs.input)}),this.$_ro.observe(this.$refs.input),this.$_ro.observe(this.$refs.list.$el)},beforeDestroy:function(){this.$_ro.disconnect()}},w=x,O=(n("892a"),p(w,r,i,!1,null,"48792d67",null));O.options.__file="VueBootstrapTypeahead.vue";var E=O.exports;e["default"]=E},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"}})["default"]});
//# sourceMappingURL=VueBootstrapTypeahead.umd.min.js.map