{% extends "pages/page.html" %}
{% load comment_tags %}
{% block meta_title %}{{ community.name }}{% endblock %}
{% block extra_css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/community.css' %}">
<link href="{% static 'css/collaborate.css' %}" rel="stylesheet" />
<link rel="stylesheet" type="text/css" href="{% static 'css/jquery.dataTables.min.css' %}">
{% endblock %}

{% block main %}
{% include 'hs_communities/communities-nav.html' with active='my_communities' %}

<!-- Load context data needed in Vue app -->
{{ data|json_script:"community-app-data" }}

{# These components needs to be included first so they are available before this app initializes #}
{% include "includes/profile-card.html" %}
{% include "hs_access_control/join-communities.html" %}

{% if community.banner %}
<div class="community-banner has-space-bottom-2x" style="background-image: url({{ community.banner }})">
</div>
{% elif community.is_czo_community %}
<div class="community-banner czo-banner has-space-bottom-2x"
  style="background-image: url({% static 'img/czo-logo-wide.png' %})">
</div>
{% endif %}

<div id="community-app" class="container">
  <div v-if="community" class="container full-width" style="padding: 0;">
    <div class="container word-break-normal">
      {% if is_admin %}
      {% include 'hs_communities/community-settings-add-owner.html' %}
      {% endif %}

      <div class="flex flex-sm-column gap-1">
        <div class="has-space-right">
          {% if community.logo %}
          <div class="group-image-wrapper medium">
            <div class="group-image" style="background-image: url({{ community.logo }})"></div>
          </div>
          {% else %}
          <div class="group-image-wrapper medium">
            <div class="group-image group-preview-image-default"
              style="background-image: url({{ STATIC_URL }}img/home-page/step4.png)">
            </div>
          </div>
          {% endif %}
        </div>

        <div class="flex-grow-1">
          <div class="community-title-container flex justify-space-between align-flex-start">
            <h2 class="community-title is-marginless">{{ community.name }}</h2>
          </div>

          {% if community.purpose %}
          <h4 class="text-muted">{{ community.purpose|linebreaks }}</h4>
          {% endif %}

          <!-- Community Info -->
          {% if community.email or community.url %}
          <div class="community-info word-break-normal">
            {% if community.url %}
            <div v-if="community.url" class="text-muted">
              <i class="fa fa-link"></i> <a :href="community.url" target="_blank">{{ community.url }}</a>
            </div>
            {% endif %}

            {% if community.email %}
            <div v-if="community.email" class="text-muted">
              <i class="fa fa-envelope-o"></i> <a :href="'mailto:' + community.email">{{ community.email }}</a>
            </div>
            {% endif %}
          </div>
          {% endif %}
        </div>
      </div>

      {% if community.description %}
      <div class="community-description has-space-top">
        {{ community.description|linebreaks }}
      </div>
      {% endif %}
    </div>

    <div class="container">
      <div class="full-width-tabs-container" id="community-tabs">
        <!-- Nav tabs -->
        <ul class="nav nav-tabs full-width-tabs" role="tablist">
          <li role="presentation" class="active">
            <a href="#resources" aria-controls="public" role="tab" data-toggle="tab">
              <i class="glyphicon glyphicon-file"></i>&nbsp;RESOURCES</a>
          </li>
          <li role="presentation">
            <a href="#groups-tab" aria-controls="groups-tab" role="tab" data-toggle="tab">
              <i class="fa fa-users"></i>&nbsp;Groups</a>
          </li>
          <li v-if="isAdmin" role="presentation">
            <a href="#settings" aria-controls="settings" role="tab" data-toggle="tab">
              <i class="fa fa-lock"></i>&nbsp;Owner Settings</a>
          </li>
        </ul>

        <!-- Tab panes -->
        <div class="tab-content" style="size: 800px">
          <!-- PUBLIC -->
          <div role="tabpanel" class="tab-pane fade in active" id="resources">
            <div class="row">
              {% include 'includes/community-resources-facets.html' %}
              {% include "includes/resource-table.html" with rows_template='includes/my-resources-trows.html' collection=community_resources %}
            </div>
          </div>
          <div role="tabpanel" class="tab-pane fade in" id="groups-tab">
            {% include 'hs_communities/community-groups.html' %}
          </div>
          <div v-if="isAdmin" role="tabpanel" class="tab-pane fade in" id="settings">
            {% include 'hs_communities/community-settings.html' %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock main %}

{% block extra_js %}
{{ block.super }}
<script type="text/javascript" src="{% static 'js/community.js' %}"></script>
<script type="text/javascript">
  var resourceTable;

  var ACTIONS_COL = 0;
  var RESOURCE_TYPE_COL = 1;
  var TITLE_COL = 2;
  var OWNER_COL = 3;
  var DATE_CREATED_COL = 4;
  var LAST_MODIFIED_COL = 5;
  var SUBJECT_COL = 6;
  var AUTHORS_COL = 7;
  var PERM_LEVEL_COL = 8;
  var LABELS_COL = 9;
  var FAVORITE_COL = 10;
  var LAST_MODIF_SORT_COL = 11;
  var SHARING_STATUS_COL = 12;
  var DATE_CREATED_SORT_COL = 13;
  var ACCESS_GRANTOR_COL = 14;

  var colDefs = [
    {
      "targets": [ACTIONS_COL],     // Row selector and controls
      "visible": false,
    },
    {
      "targets": [RESOURCE_TYPE_COL],     // Resource type
      "width": "100px"
    },
    {
      "targets": [ACTIONS_COL],     // Actions
      "orderable": false,
      "searchable": false,
      "width": "70px"
    },
    {
      "targets": [LAST_MODIFIED_COL],     // Last modified
      "iDataSort": LAST_MODIF_SORT_COL
    },
    {
      "targets": [DATE_CREATED_COL],     // Created
      "iDataSort": DATE_CREATED_SORT_COL
    },
    {
      "targets": [SUBJECT_COL],     // Subject
      "visible": false,
      "searchable": true
    },
    {
      "targets": [AUTHORS_COL],     // Authors
      "visible": false,
      "searchable": true
    },
    {
      "targets": [PERM_LEVEL_COL],     // Permission level
      "visible": false,
      "searchable": true
    },
    {
      "targets": [LABELS_COL],     // Labels
      "visible": false,
      "searchable": true
    },
    {
      "targets": [FAVORITE_COL],     // Favorite
      "visible": false,
      "searchable": true
    },
    {
      "targets": [LAST_MODIF_SORT_COL],     // Last modified (for sorting)
      "visible": false,
      "searchable": true
    },
    {
      "targets": [DATE_CREATED_SORT_COL],     // Last modified (for sorting)
      "visible": false,
      "searchable": true
    },
    {
      "targets": [SHARING_STATUS_COL],     // Sharing status
      "visible": false,
      "searchable": false
    },
    {
      "targets": [ACCESS_GRANTOR_COL], // Access Grantor
      "visible": false,
      "searchable": false,
    },
  ];

</script>
<script>
  // https://github.com/select2/select2/issues/1436#issuecomment-21028474
  // incompatibility between Select2 and Bootstrap modals
  $.fn.modal.Constructor.prototype.enforceFocus = function() {};
</script>
{% endblock %}