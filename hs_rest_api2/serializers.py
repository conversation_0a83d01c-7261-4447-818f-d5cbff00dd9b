import json

from drf_yasg.generators import OpenAPISchemaGenerator
from hsmodels.schemas import (FileSetMetadata, GeographicFeatureMetadata,
                              GeographicRasterMetadata,
                              MultidimensionalMetadata,
                              ReferencedTimeSeriesMetadata, ResourceMetadata,
                              SingleFileMetadata, TimeSeriesMetadata, CSVFileMetadata)
from hsmodels.schemas.aggregations import (FileSetMetadataIn,
                                           GeographicFeatureMetadataIn,
                                           GeographicRasterMetadataIn,
                                           ModelInstanceMetadata,
                                           ModelInstanceMetadataIn,
                                           ModelProgramMetadata,
                                           ModelProgramMetadataIn,
                                           MultidimensionalMetadataIn,
                                           ReferencedTimeSeriesMetadataIn,
                                           SingleFileMetadataIn,
                                           TimeSeriesMetadataIn,
                                           CSVFileMetadataIn,)
from hsmodels.schemas.resource import ResourceMetadataIn
from pydantic import ConfigDict
from rest_framework.serializers import Serializer
from rest_framework import serializers


def get_schema_open_api_v2(schema):
    # replace $defs with definitions in schema to make it compatible with openapi v2
    schema_str = json.dumps(schema)
    schema_str = schema_str.replace('$defs', 'definitions')
    schema = json.loads(schema_str)
    return schema


class ResourceMetadataInForbidExtra(ResourceMetadataIn):
    model_config = ConfigDict(extra="forbid")


class ResourceMetadataSerializer(Serializer):
    class Meta:
        fields = "__all__"
        swagger_schema_fields = get_schema_open_api_v2(ResourceMetadata.model_json_schema())


class ResourceMetadataInSerializer(Serializer):
    class Meta:
        fields = "__all__"
        _schema = ResourceMetadataInForbidExtra.model_json_schema()
        _schema['title'] = _schema['title'] + " In"
        swagger_schema_fields = get_schema_open_api_v2(_schema)


class GeographicFeatureMetadataSerializer(Serializer):
    class Meta:
        fields = "__all__"
        swagger_schema_fields = get_schema_open_api_v2(GeographicFeatureMetadata.model_json_schema())


class GeographicFeatureMetadataInSerializer(Serializer):
    class Meta:
        fields = "__all__"
        _schema = GeographicFeatureMetadataIn.model_json_schema()
        _schema['title'] = _schema['title'] + " In"
        swagger_schema_fields = get_schema_open_api_v2(_schema)


class GeographicRasterMetadataSerializer(Serializer):
    class Meta:
        fields = "__all__"
        swagger_schema_fields = get_schema_open_api_v2(GeographicRasterMetadata.model_json_schema())


class GeographicRasterMetadataInSerializer(Serializer):
    class Meta:
        fields = "__all__"
        _schema = GeographicRasterMetadataIn.model_json_schema()
        _schema['title'] = _schema['title'] + " In"
        swagger_schema_fields = get_schema_open_api_v2(_schema)


class MultidimensionalMetadataSerializer(Serializer):
    class Meta:
        fields = "__all__"
        swagger_schema_fields = get_schema_open_api_v2(MultidimensionalMetadata.model_json_schema())


class MultidimensionalMetadataInSerializer(Serializer):
    class Meta:
        fields = "__all__"
        _schema = MultidimensionalMetadataIn.model_json_schema()
        _schema['title'] = _schema['title'] + " In"
        swagger_schema_fields = get_schema_open_api_v2(_schema)


class SingleFileMetadataSerializer(Serializer):
    class Meta:
        fields = "__all__"
        swagger_schema_fields = get_schema_open_api_v2(SingleFileMetadata.model_json_schema())


class SingleFileMetadataInSerializer(Serializer):
    class Meta:
        fields = "__all__"
        _schema = SingleFileMetadataIn.model_json_schema()
        _schema['title'] = _schema['title'] + " In"
        swagger_schema_fields = get_schema_open_api_v2(_schema)


class CSVFileMetadataSerializer(Serializer):
    class Meta:
        fields = "__all__"
        swagger_schema_fields = get_schema_open_api_v2(CSVFileMetadata.model_json_schema())


class CSVFileMetadataInSerializer(Serializer):
    class Meta:
        fields = "__all__"
        _schema = CSVFileMetadataIn.model_json_schema()
        _schema['title'] = _schema['title'] + " In"
        swagger_schema_fields = get_schema_open_api_v2(_schema)


class FileSetMetadataSerializer(Serializer):
    class Meta:
        fields = "__all__"
        swagger_schema_fields = get_schema_open_api_v2(FileSetMetadata.model_json_schema())


class FileSetMetadataInSerializer(Serializer):
    class Meta:
        fields = "__all__"
        _schema = FileSetMetadataIn.model_json_schema()
        _schema['title'] = _schema['title'] + " In"
        swagger_schema_fields = get_schema_open_api_v2(_schema)


class TimeSeriesMetadataSerializer(Serializer):
    class Meta:
        fields = "__all__"
        swagger_schema_fields = get_schema_open_api_v2(TimeSeriesMetadata.model_json_schema())


class TimeSeriesMetadataInSerializer(Serializer):
    class Meta:
        fields = "__all__"
        _schema = TimeSeriesMetadataIn.model_json_schema()
        _schema['title'] = _schema['title'] + " In"
        swagger_schema_fields = get_schema_open_api_v2(_schema)


class ReferencedTimeSeriesMetadataSerializer(Serializer):
    class Meta:
        fields = "__all__"
        swagger_schema_fields = get_schema_open_api_v2(ReferencedTimeSeriesMetadata.model_json_schema())


class ReferencedTimeSeriesMetadataInSerializer(Serializer):
    class Meta:
        fields = "__all__"
        _schema = ReferencedTimeSeriesMetadataIn.model_json_schema()
        _schema['title'] = _schema['title'] + " In"
        swagger_schema_fields = get_schema_open_api_v2(_schema)


class ModelProgramMetadataSerializer(Serializer):
    class Meta:
        fields = "__all__"
        swagger_schema_fields = get_schema_open_api_v2(ModelProgramMetadata.model_json_schema())


class ModelProgramMetadataInSerializer(Serializer):
    class Meta:
        fields = "__all__"
        _schema = ModelProgramMetadataIn.model_json_schema()
        _schema['title'] = _schema['title'] + " In"
        swagger_schema_fields = get_schema_open_api_v2(_schema)


class ModelInstanceMetadataSerializer(Serializer):
    class Meta:
        fields = "__all__"
        swagger_schema_fields = get_schema_open_api_v2(ModelInstanceMetadata.model_json_schema())


class ModelInstanceMetadataInSerializer(Serializer):
    class Meta:
        fields = "__all__"
        _schema = ModelInstanceMetadataIn.model_json_schema()
        _schema['title'] = _schema['title'] + " In"
        swagger_schema_fields = get_schema_open_api_v2(_schema)


class ResourceMetadataFileBasedSerializer(Serializer):
    """
    Serializer for resource metadata returned by resource.metadata.to_json() method.
    This serializer handles the dynamic structure of metadata returned by the file-based approach.
    """
    class Meta:
        fields = "__all__"
        swagger_schema_fields = {
            "type": "object",
            "title": "Resource Metadata File Based",
            "description": "Resource metadata using file-based approach (to_json method)",
            "properties": {
                "title": {
                    "type": "string",
                    "description": "Resource title"
                },
                "description": {
                    "type": "string",
                    "description": "Resource description/abstract"
                },
                "language": {
                    "type": "string",
                    "description": "Resource language code"
                },
                "type": {
                    "type": "string",
                    "description": "Resource type"
                },
                "publisher": {
                    "type": "string",
                    "description": "Resource publisher"
                },
                "rights": {
                    "type": "object",
                    "description": "Rights information",
                    "properties": {
                        "statement": {"type": "string"},
                        "url": {"type": "string"}
                    }
                },
                "creators": {
                    "type": "array",
                    "description": "Resource creators",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "organization": {"type": "string"},
                            "email": {"type": "string"},
                            "hydroshare_user_id": {"type": "integer"},
                            "identifiers": {"type": "object"},
                            "creator_order": {"type": "integer"}
                        }
                    }
                },
                "contributors": {
                    "type": "array",
                    "description": "Resource contributors",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "organization": {"type": "string"},
                            "email": {"type": "string"},
                            "phone": {"type": "string"},
                            "address": {"type": "string"},
                            "homepage": {"type": "string"},
                            "identifiers": {"type": "object"}
                        }
                    }
                },
                "dates": {
                    "type": "array",
                    "description": "Resource dates",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "string"},
                            "start_date": {"type": "string"},
                            "end_date": {"type": "string"}
                        }
                    }
                },
                "coverages": {
                    "type": "array",
                    "description": "Spatial and temporal coverages",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "string"},
                            "name": {"type": "string"},
                            "northlimit": {"type": "number"},
                            "southlimit": {"type": "number"},
                            "eastlimit": {"type": "number"},
                            "westlimit": {"type": "number"},
                            "units": {"type": "string"},
                            "projection": {"type": "string"},
                            "start": {"type": "string"},
                            "end": {"type": "string"}
                        }
                    }
                },
                "identifiers": {
                    "type": "array",
                    "description": "Resource identifiers",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "url": {"type": "string"}
                        }
                    }
                },
                "keywords": {
                    "type": "array",
                    "description": "Resource keywords/subjects",
                    "items": {"type": "string"}
                },
                "relations": {
                    "type": "array",
                    "description": "Resource relations",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "string"},
                            "value": {"type": "string"}
                        }
                    }
                },
                "geospatialrelations": {
                    "type": "array",
                    "description": "Geospatial relations",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "string"},
                            "value": {"type": "string"},
                            "text": {"type": "string"}
                        }
                    }
                },
                "funding_agencies": {
                    "type": "array",
                    "description": "Funding agencies/awards",
                    "items": {
                        "type": "object",
                        "properties": {
                            "funding_agency_name": {"type": "string"},
                            "title": {"type": "string"},
                            "number": {"type": "string"},
                            "funding_agency_url": {"type": "string"}
                        }
                    }
                },
                "additional_metadata": {
                    "type": "object",
                    "description": "Additional key-value metadata"
                },
                "sharing_status": {
                    "type": "string",
                    "description": "Resource sharing status"
                },
                "citation": {
                    "type": "string",
                    "description": "Resource citation"
                },
                "content_files": {
                    "type": "array",
                    "description": "Content files (for CompositeResource)",
                    "items": {
                        "type": "object",
                        "properties": {
                            "path": {"type": "string"},
                            "size": {"type": "integer"},
                            "mime_type": {"type": "string"},
                            "checksum": {"type": "string"}
                        }
                    }
                },
                "aggregations": {
                    "type": "array",
                    "description": "Logical file aggregations (for CompositeResource)",
                    "items": {
                        "type": "object",
                        "properties": {
                            "path": {"type": "string"},
                            "type": {"type": "string"}
                        }
                    }
                }
            }
        }


class GenericLogicalFileMetadataFileBasedSerializer(Serializer):
    """
    Serializer for GenericLogicalFile metadata returned by logical_file.metadata.to_json() method.
    This serializer handles the dynamic structure of logical file metadata returned by the file-based approach.
    """
    class Meta:
        fields = "__all__"
        swagger_schema_fields = {
            "type": "object",
            "title": "Generic Logical File Metadata File Based",
            "description": "Generic logical file metadata using file-based approach (to_json method)",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Logical file name/title"
                },
                "description": {
                    "type": "string",
                    "description": "Logical file description"
                },
                "keywords": {
                    "type": "array",
                    "description": "Logical file keywords",
                    "items": {"type": "string"}
                },
                "extra_metadata": {
                    "type": "object",
                    "description": "Additional key-value metadata"
                },
                "coverages": {
                    "type": "array",
                    "description": "Spatial and temporal coverages",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "string"},
                            "name": {"type": "string"},
                            "northlimit": {"type": "number"},
                            "southlimit": {"type": "number"},
                            "eastlimit": {"type": "number"},
                            "westlimit": {"type": "number"},
                            "units": {"type": "string"},
                            "projection": {"type": "string"},
                            "start": {"type": "string"},
                            "end": {"type": "string"}
                        }
                    }
                },
                "content_files": {
                    "type": "array",
                    "description": "Files that are part of this logical file",
                    "items": {
                        "type": "object",
                        "properties": {
                            "path": {"type": "string"},
                            "size": {"type": "integer"},
                            "mime_type": {"type": "string"},
                            "checksum": {"type": "string"}
                        }
                    }
                },
                "@context": {
                    "type": "object",
                    "description": "JSON-LD context"
                },
                "@type": {
                    "type": "string",
                    "description": "JSON-LD type"
                }
            }
        }


class NestedSchemaGenerator(OpenAPISchemaGenerator):
    """
    Injects hsmodels metadata models nested definitions into the root definitions swagger instance
    """

    def get_schema(self, request=None, public=False):
        swagger = super(NestedSchemaGenerator, self).get_schema(request, public)
        for model in [ResourceMetadata, GeographicFeatureMetadata, GeographicRasterMetadata, MultidimensionalMetadata,
                      SingleFileMetadata, FileSetMetadata, TimeSeriesMetadata, ReferencedTimeSeriesMetadata,
                      ModelProgramMetadata, CSVFileMetadata]:
            schema = model.model_json_schema()
            schema = get_schema_open_api_v2(schema)

            for d in schema['definitions']:
                swagger.definitions.update({d: schema['definitions'][d]})
        return swagger


class ResourceSharingStatusSerializer(Serializer):
    sharing_status = serializers.ChoiceField(choices=['public', 'private', 'discoverable', 'published'])


class ResourcePermissionSerializer(Serializer):
    permission = serializers.ChoiceField(choices=['owner', 'edit', 'view', 'none'])
