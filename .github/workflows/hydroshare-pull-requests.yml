name: Test HydroShare Pull Requests

on:
  push:
    branches: [ develop ]
  pull_request:
    branches: 
      - '**'
  workflow_dispatch:

jobs:
  hydroshare-pull-request-tests:

    runs-on: ubuntu-latest
    #env:
    #  COVERAGE_TOTAL: 10

    steps:
    - uses: actions/checkout@v4

    - name: Run flake8
      run: |
        python -m pip install flake8
        ./run-pylint-jenkins

    - name: Install Docker Compose
      run: |
        sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose

    - name: Setup HydroShare
      run: |
        printf "%s\n" c | ./local-dev-first-start-only.sh


    - name: Run HydroShare Tests
      run: |
        docker exec hydroshare ./run-tests-jenkins
