name: Locust tests on demand

on:
  workflow_dispatch:
    inputs:
      target:
        description: 'ORIGIN to target tests (default: https://beta.hydroshare.org).'
        required: false
        default: 'https://beta.hydroshare.org'
      users:
        description: 'Number of users to simulate (default: 10).'
        required: false
        default: '10'
      spawn_rate:
        description: 'Number of users to spawn per second (default: 1).'
        required: false
        default: '1'
      run_time:
        description: 'Duration of the test (default: 1m).'
        required: false
        default: '1m'

jobs:
  build:

    strategy:
      matrix:
        python-version: [3.9]
        platform: [ubuntu-latest]

    runs-on: ${{ matrix.platform }}

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r locust/requirements.txt

    - name: Run Tests against target = ${{ github.event.inputs.target }}
      if: github.event.inputs.target != ''
      env:
        HS_USERNAME: ${{ secrets.HS_USERNAME }}
        HS_PASSWORD: ${{ secrets.HS_PASSWORD }}
        LOCUST_LOCUSTFILE: locust/locustfile.py
        LOCUST_HEADLESS: true
        LOCUST_ONLY_SUMMARY: true
        LOCUST_CSV: locust/reports/csv/locust-report
        LOCUST_HTML: locust/reports/locust-report.html
        LOCUST_HOST: ${{ github.event.inputs.target }}
        LOCUST_USERS: ${{ github.event.inputs.users }}
        LOCUST_SPAWN_RATE: ${{ github.event.inputs.spawn_rate }}
        LOCUST_RUN_TIME: ${{ github.event.inputs.run_time }}
      run: |
        # even if exit code 1, continue to upload artifacts
        locust || true
    
    - name: Zip reports
      run: |
        cd locust/reports/csv/
        zip -r locust-report-csv.zip *.csv
        mv locust-report-csv.zip ../locust-report-csv.zip
        cd ../..
    
    - name: Archive Load Results
      uses: actions/upload-artifact@v4
      with:
        name: locust-results
        path: |
          locust/reports/locust-report.html
          locust/reports/locust-report-csv.zip
    
    - name: Check for errors
      run: |
        if [ -f locust/reports/csv/locust-report_failures.csv ]; then
            if tail -n +2 locust/reports/csv/locust-report_failures.csv | grep -q 'Error'; then
            echo "Errors found in locust report"
            exit 1
          fi
        fi