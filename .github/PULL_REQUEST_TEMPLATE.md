<!--

Please read, and add your text at the bottom of this message.

Thanks for contributing code to HydroShare. In order to maintain code quality and expedite this process, please assist the development team by making sure the following is present in this pull request.

For more information, see https://docs.google.com/document/d/1dzxqlZW5fKNEyQSeKiSFq-SmS-VOPCva95XXkBjPExs

-->

### Pull Request Checklist: 
- [ ] Positive Test Case Written by Dev

<!-- Enter steps that a QA engineer, stakeholder, or user documentation writer would follow to test the positive or "successful" case of the functionality your code provides or fixes -->

- [ ] Automated Testing

<!-- Our Jenkins Instance is set up to automatically test every commit from a pull request. Code coverage must not decrease so new functionality or code paths added during a bug fix must have appropriate tests written. Every test must pass, including PEP8 code formatting tests. -->

- [ ] Sufficient User and Developer Documentation

### Positive Test Case
1. [Enter positive test case here]
