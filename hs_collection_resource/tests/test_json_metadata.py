import json
import os

from django.contrib.auth.models import Group
from django.test import TransactionTestCase

from hs_core.hydroshare import create_resource, create_account
from hs_core.hydroshare.resource import save_resource_metadata_json
from hs_core.testing import MockS3TestCaseMixin
from hs_file_types.enums import AggregationMetaFilePath


class TestCollectionJSONMetadata(MockS3TestCaseMixin, TransactionTestCase):
    """Test JSON metadata generation for collection resources."""

    def setUp(self):
        super(TestCollectionJSONMetadata, self).setUp()
        self.group, _ = Group.objects.get_or_create(name='Hydroshare Author')

        self.user = create_account(
            '<EMAIL>',
            username='testuser',
            password='testpassword',
            first_name='Test',
            last_name='User',
            superuser=False,
            groups=[self.group]
        )

        # Create a collection resource
        self.collection_resource = create_resource(
            resource_type='CollectionResource',
            owner=self.user,
            title='Test Collection Resource',
            keywords=['collection', 'test', 'json'],
            metadata=[
                {"rights": {"statement": "This is a test statement", "url": "http://example.com"}},
                {"description": {"abstract": "This is a test collection resource for JSON metadata testing"}}
            ]
        )

        # Create some resources to add to the collection
        self.resource1 = create_resource(
            resource_type='CompositeResource',
            owner=self.user,
            title='Test Resource 1',
            keywords=['resource1', 'test']
        )

        self.resource2 = create_resource(
            resource_type='CompositeResource',
            owner=self.user,
            title='Test Resource 2',
            keywords=['resource2', 'test']
        )

    def tearDown(self):
        super(TestCollectionJSONMetadata, self).tearDown()
        self.collection_resource.delete()
        self.resource1.delete()
        self.resource2.delete()

    def test_collection_resource_metadata_json_generation(self):
        """Test resource-level metadata generation and JSON file saving to S3 for collection resources."""

        # Add metadata to the collection resource
        self.collection_resource.metadata.create_element(
            'coverage', type='period', value={'start': '2020-01-01', 'end': '2020-12-31'}
        )
        self.collection_resource.metadata.create_element(
            'coverage', type='point', value={'east': '56.45678', 'north': '12.6789', 'units': 'Decimal degrees'}
        )
        self.collection_resource.metadata.create_element('creator', name='John Smith')
        self.collection_resource.metadata.create_element('contributor', name='Jane Doe')

        # Generate and save metadata JSON
        save_resource_metadata_json(self.collection_resource)

        # Check if the JSON file exists in S3
        istorage = self.collection_resource.get_s3_storage()
        json_file_path = os.path.join(
            self.collection_resource.file_path,
            AggregationMetaFilePath.METADATA_JSON_FILE_NAME
        )
        # check the resource metadata json file path
        self.assertEqual(self.collection_resource.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test Collection Resource')
        self.assertEqual(metadata_dict['description'], 'This is a test collection resource for JSON metadata testing')
        self.assertIn('collection', metadata_dict['keywords'])
        self.assertIn('test', metadata_dict['keywords'])
        self.assertIn('json', metadata_dict['keywords'])

        # Check creators and contributors
        self.assertEqual(len(metadata_dict['creator']), 2)  # User + John Smith
        self.assertEqual(len(metadata_dict['contributor']), 1)
        self.assertEqual(metadata_dict['contributor'][0]['name'], 'Jane Doe')

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoCoordinates')
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['latitude'], 12.6789)
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['longitude'], 56.45678)

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2020-01-01')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2020-12-31')

        # Check hasPart relation - should be missing as there are no contained resources
        self.assertNotIn('hasPart', metadata_dict)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_collection_resource_with_contained_resources_json_metadata(self):
        """Test JSON metadata generation for collection resources with contained resources."""

        # Add resources to the collection
        self.collection_resource.resources.add(self.resource1)
        self.collection_resource.resources.add(self.resource2)

        # Add the isPartOf relation metadata to the contained resources
        self.resource1.metadata.create_element('relation', type='isPartOf', value=self.collection_resource.get_citation())
        self.resource2.metadata.create_element('relation', type='isPartOf', value=self.collection_resource.get_citation())

        # Add hasPart relation metadata to the collection resource
        self.collection_resource.metadata.create_element('relation', type='hasPart', value=self.resource1.get_citation())
        self.collection_resource.metadata.create_element('relation', type='hasPart', value=self.resource2.get_citation())

        # Generate and save metadata JSON for the collection resource
        save_resource_metadata_json(self.collection_resource)

        # Check if the JSON file exists in S3
        istorage = self.collection_resource.get_s3_storage()
        json_file_path = os.path.join(
            self.collection_resource.file_path,
            AggregationMetaFilePath.METADATA_JSON_FILE_NAME
        )
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check hasPart relation for contained resources
        self.assertIn('hasPart', metadata_dict)
        self.assertEqual(len(metadata_dict['hasPart']), 2)

        # Check that the hasPart citations point to the correct resources citations
        hasPart_citations = [has_part["text"] for has_part in metadata_dict['hasPart']]
        self.assertIn(self.resource1.get_citation(), hasPart_citations)
        self.assertIn(self.resource2.get_citation(), hasPart_citations)

        # Check that the contained resources have isPartOf relation pointing to the collection
        # First, generate JSON metadata for the contained resources
        save_resource_metadata_json(self.resource1)
        save_resource_metadata_json(self.resource2)

        # Check resource1 metadata
        json_file_path1 = os.path.join(
            self.resource1.file_path,
            AggregationMetaFilePath.METADATA_JSON_FILE_NAME
        )
        json_content1 = istorage.open(json_file_path1).read().decode('utf-8')
        metadata_dict1 = json.loads(json_content1)

        # Check isPartOf relation for resource1 pointing to the collection citation
        self.assertIn('isPartOf', metadata_dict1)
        self.assertEqual(len(metadata_dict1['isPartOf']), 1)
        self.assertEqual(metadata_dict1['isPartOf'][0]["text"], self.collection_resource.get_citation())

        # Check resource2 metadata
        json_file_path2 = os.path.join(
            self.resource2.file_path,
            AggregationMetaFilePath.METADATA_JSON_FILE_NAME
        )
        json_content2 = istorage.open(json_file_path2).read().decode('utf-8')
        metadata_dict2 = json.loads(json_content2)

        # Check isPartOf relation for resource2 pointing to the collection citation
        self.assertIn('isPartOf', metadata_dict2)
        self.assertEqual(len(metadata_dict2['isPartOf']), 1)
        self.assertEqual(metadata_dict2['isPartOf'][0]["text"], self.collection_resource.get_citation())
