# coding=utf-8

from django.test import TransactionTestCase
from django.contrib.auth.models import Group
from django.core.files.uploadedfile import UploadedFile

from hs_core.testing import MockS3TestCaseMixin
from hs_core import hydroshare
from hs_core.hydroshare.utils import resource_file_add_process

from hs_file_types.models import (
    GeoRasterLogicalFile,
    NetCDFLogicalFile,
    RefTimeseriesLogicalFile,
    GeoFeatureLogicalFile,
    TimeSeriesLogicalFile,
    CSVLogicalFile,
)
from hs_file_types.tests.utils import CompositeResourceTestMixin


class CompositeResourceTestAutoAggregate(MockS3TestCaseMixin, TransactionTestCase,
                                         CompositeResourceTestMixin):
    def setUp(self):
        super(CompositeResourceTestAutoAggregate, self).setUp()
        self.group, _ = Group.objects.get_or_create(name='Hydroshare Author')
        self.user = hydroshare.create_account(
            '<EMAIL>',
            username='user1',
            first_name='Creator_FirstName',
            last_name='Creator_LastName',
            superuser=False,
            groups=[self.group]
        )

        self.res_title = 'Testing Composite Resource'
        self.test_file_path = 'hs_composite_resource/tests/data/{}'

    def tearDown(self):
        self.composite_resource.delete()

    def test_auto_aggregate_on_create_tif(self):
        """test that auto-aggregate works on resource create with .tif"""

        self.assertEqual(0, GeoRasterLogicalFile.objects.count())

        self.create_composite_resource(file_to_upload=self.test_file_path.format(
            "small_logan.tif"), auto_aggregate=True)

        self.assertEqual(1, GeoRasterLogicalFile.objects.count())

    def test_auto_aggregate_on_create_tiff(self):
        """test that auto-aggregate works on resource create with .tiff"""

        self.assertEqual(0, GeoRasterLogicalFile.objects.count())

        self.create_composite_resource(file_to_upload=self.test_file_path.format(
            "small_logan.tiff"), auto_aggregate=True)

        self.assertEqual(1, GeoRasterLogicalFile.objects.count())

    def test_auto_aggregate_on_create_nc(self):
        """test that auto-aggregate works on resource create with .nc"""

        self.assertEqual(0, NetCDFLogicalFile.objects.count())

        self.create_composite_resource(file_to_upload=self.test_file_path.format(
            "netcdf_valid.nc"), auto_aggregate=True)

        self.assertEqual(1, NetCDFLogicalFile.objects.count())

    def test_auto_aggregate_on_create_geo_feature(self):
        """test that auto-aggregate works on resource create with geo feature"""

        self.assertEqual(0, GeoFeatureLogicalFile.objects.count())

        self.create_composite_resource(file_to_upload=[
            self.test_file_path.format("watersheds.dbf"),
            self.test_file_path.format("watersheds.shp"),
            self.test_file_path.format("watersheds.shx")], auto_aggregate=True)

        self.assertEqual(1, GeoFeatureLogicalFile.objects.count())

    def test_auto_aggregate_on_create_refts(self):
        """test that auto-aggregate works on resource create with refts"""

        self.assertEqual(0, RefTimeseriesLogicalFile.objects.count())

        self.create_composite_resource(file_to_upload=[
            self.test_file_path.format("multi_sites_formatted_version1.0.refts.json")],
            auto_aggregate=True)

        self.assertEqual(1, RefTimeseriesLogicalFile.objects.count())

    def test_auto_aggregate_on_create_time_series(self):
        """test that auto-aggregate works on resource create with time series"""

        self.assertEqual(0, TimeSeriesLogicalFile.objects.count())

        self.create_composite_resource(file_to_upload=[
            self.test_file_path.format("ODM2.sqlite")],
            auto_aggregate=True)

        self.assertEqual(1, TimeSeriesLogicalFile.objects.count())

    def test_auto_aggregate_file_add_tif(self):
        """test that auto-aggregate works on tif file add"""

        self.create_composite_resource()

        self.assertEqual(0, GeoRasterLogicalFile.objects.count())

        # test add a file that auto-aggregates
        open_file = open(self.test_file_path.format("small_logan.tif"), 'rb')
        resource_file_add_process(resource=self.composite_resource,
                                  files=(open_file,), user=self.user)

        self.assertEqual(1, GeoRasterLogicalFile.objects.count())

    def test_auto_aggregate_file_add_tiff(self):
        """test that auto-aggregate works on tiff file add"""

        self.create_composite_resource()

        self.assertEqual(0, GeoRasterLogicalFile.objects.count())

        # test add a file that auto-aggregates
        open_file = open(self.test_file_path.format("small_logan.tiff"), 'rb')
        resource_file_add_process(resource=self.composite_resource,
                                  files=(open_file,), user=self.user)

        self.assertEqual(1, GeoRasterLogicalFile.objects.count())

    def test_auto_aggregate_file_add_sqlite(self):
        """test that auto-aggregate works on sqlite file add"""

        self.create_composite_resource()

        self.assertEqual(0, TimeSeriesLogicalFile.objects.count())

        # test add a file that auto-aggregates
        open_file = open(self.test_file_path.format("ODM2.sqlite"), 'rb')
        resource_file_add_process(resource=self.composite_resource,
                                  files=(open_file,), user=self.user)

        self.assertEqual(1, TimeSeriesLogicalFile.objects.count())

    def test_auto_aggregate_file_add_refts(self):
        """test that auto-aggregate works on refts file add"""

        self.create_composite_resource()

        self.assertEqual(0, RefTimeseriesLogicalFile.objects.count())
        # test add a file that auto-aggregates
        open_file = open(self.test_file_path.format("multi_sites_formatted_version1.0.refts.json"),
                         'rb')
        resource_file_add_process(resource=self.composite_resource,
                                  files=(open_file,), user=self.user)

        self.assertEqual(1, RefTimeseriesLogicalFile.objects.count())

    def test_auto_aggregate_file_add_nc(self):
        """test that auto-aggregate works on nc file add"""

        self.create_composite_resource()

        self.assertEqual(0, NetCDFLogicalFile.objects.count())
        # test add a file that auto-aggregates
        open_file = open(self.test_file_path.format("netcdf_valid.nc"), 'rb')
        resource_file_add_process(resource=self.composite_resource,
                                  files=(open_file,), user=self.user)
        # because of auto aggregation, there should be 2 files

        self.assertEqual(1, NetCDFLogicalFile.objects.count())

    def test_auto_aggregate_file_add_geo_feature(self):
        """test that auto-aggregate works on geo feature file add"""

        self.create_composite_resource()

        self.assertEqual(0, GeoFeatureLogicalFile.objects.count())

        # test add a file that auto-aggregates
        dbf_file = open(self.test_file_path.format("watersheds.dbf"), 'rb')
        shp_file = open(self.test_file_path.format("watersheds.shp"), 'rb')
        shx_file = open(self.test_file_path.format("watersheds.shx"), 'rb')
        resource_file_add_process(resource=self.composite_resource,
                                  files=(dbf_file, shp_file, shx_file), user=self.user)

        self.assertEqual(1, GeoFeatureLogicalFile.objects.count())

    def test_auto_aggregate_on_create_files_with_folder(self):
        """test that auto-aggregate works on resource create with folders"""

        self.assertEqual(0, GeoFeatureLogicalFile.objects.count())

        self.create_composite_resource(file_to_upload=[
            self.test_file_path.format("watersheds.dbf"),
            self.test_file_path.format("watersheds.shp"),
            self.test_file_path.format("watersheds.shx")], auto_aggregate=True, folder="folder")

        self.assertEqual(1, GeoFeatureLogicalFile.objects.count())

        storage_paths = ["folder/watersheds.dbf", "folder/watersheds.shp", "folder/watersheds.shx"]
        for res_file in self.composite_resource.files.all():
            index = -1
            for i, name in enumerate(storage_paths):
                if name == res_file.storage_path:
                    index = i
                    break
            del storage_paths[index]

        self.assertEqual(0, len(storage_paths))

    def test_auto_aggregate_files_add_with_different_folder(self):
        """test adding files in different folders"""

        self.create_composite_resource()

        self.assertEqual(0, GeoFeatureLogicalFile.objects.count())

        # test add a file that auto-aggregates
        dbf_file = open(self.test_file_path.format("watersheds.dbf"), 'rb')
        shp_file = open(self.test_file_path.format("watersheds.shp"), 'rb')
        shx_file = open(self.test_file_path.format("watersheds.shx"), 'rb')
        resource_file_add_process(resource=self.composite_resource,
                                  files=(dbf_file, shp_file, shx_file), user=self.user,
                                  full_paths={dbf_file: "folder1/watersheds.dbf",
                                              shp_file: "folder2/watersheds.shp",
                                              shx_file: "folder2/watersheds.shx"})

        # because the files are in different folders, auto aggreate won't work

        self.assertEqual(0, GeoFeatureLogicalFile.objects.count())

        storage_paths = ["folder1/watersheds.dbf", "folder2/watersheds.shp",
                         "folder2/watersheds.shx"]
        for res_file in self.composite_resource.files.all():
            index = -1
            for i, name in enumerate(storage_paths):
                if name == res_file.storage_path:
                    index = i
                    break
            del storage_paths[index]

        self.assertEqual(0, len(storage_paths))

    def test_auto_aggregate_files_add_child_folder(self):
        """test adding files in different folders"""

        self.create_composite_resource()

        self.assertEqual(0, GeoFeatureLogicalFile.objects.count())

        # test add a file that auto-aggregates
        dbf_file = open(self.test_file_path.format("watersheds.dbf"), 'rb')
        shp_file = open(self.test_file_path.format("watersheds.shp"), 'rb')
        shx_file = open(self.test_file_path.format("watersheds.shx"), 'rb')
        resource_file_add_process(resource=self.composite_resource,
                                  folder="parentfolder",
                                  files=(dbf_file, shp_file, shx_file), user=self.user,
                                  full_paths={dbf_file: "folder/watersheds.dbf",
                                              shp_file: "folder/watersheds.shp",
                                              shx_file: "folder/watersheds.shx"})

        # because the files are in different folders, auto aggreate won't work

        self.assertEqual(1, GeoFeatureLogicalFile.objects.count())

        storage_paths = ["parentfolder/folder/watersheds.dbf",
                         "parentfolder/folder/watersheds.shp",
                         "parentfolder/folder/watersheds.shx"]
        for res_file in self.composite_resource.files.all():
            index = -1
            for i, name in enumerate(storage_paths):
                if name == res_file.storage_path:
                    index = i
                    break
            del storage_paths[index]

        self.assertEqual(0, len(storage_paths))

    def test_auto_aggregate_csv_file_add_to_root(self):
        """test adding a csv file to root folder should not create a timeseries aggregation, but should create
        a CSV aggregation"""

        self._test_csv_file_add()

    def test_auto_aggregate_csv_file_add_to_folder(self):
        """test adding a csv file to a folder should not create a timeseries aggregation, but should create a
        CSV aggregation"""

        self._test_csv_file_add(new_folder="csv_folder")

    def _test_csv_file_add(self, new_folder=''):
        self.create_composite_resource()
        # no timeseries aggregation prior to adding the csv file to a folder
        self.assertEqual(0, TimeSeriesLogicalFile.objects.count())
        if new_folder:
            # create a folder
            hydroshare.ResourceFile.create_folder(self.composite_resource, folder=new_folder)
        # upload the csv file to the folder (new-folder)
        csv_file_name = "ODM2_One_Site_One_Series_Test.csv"
        csv_file = open(self.test_file_path.format(csv_file_name), 'rb')
        files = [UploadedFile(file=csv_file, name=csv_file_name)]
        resource_file_add_process(resource=self.composite_resource,
                                  files=files, user=self.user, folder=new_folder)

        # check that the resource has one file
        self.assertEqual(self.composite_resource.files.all().count(), 1)
        csv_res_file = self.composite_resource.files.first()
        self.assertEqual(csv_res_file.file_folder, new_folder)
        # no timeseries aggregation after adding the scv file to a folder
        self.assertEqual(0, TimeSeriesLogicalFile.objects.count())
        # should create a CSV aggregation
        self.assertEqual(1, CSVLogicalFile.objects.count())
