import os
import json

from django.test import TransactionTestCase
from django.contrib.auth.models import Group
from django.core.files.uploadedfile import UploadedFile

from hs_core import hydroshare
from hs_core.hydroshare.resource import save_resource_metadata_json
from hs_core.hydroshare import add_file_to_resource, ResourceFile
from hs_core.testing import MockS3TestCaseMixin
from hs_file_types.models import (
    GenericLogicalFile, GeoRasterLogicalFile, NetCDFLogicalFile,
    GeoFeatureLogicalFile, TimeSeriesLogicalFile, FileSetLogicalFile,
    ModelProgramLogicalFile, ModelInstanceLogicalFile, RefTimeseriesLogicalFile,
    CSVLogicalFile
)
from hs_file_types.enums import AggregationMetaFilePath
from hs_file_types.models.model_program import ModelProgramResourceFileType


class TestJSONMetadata(MockS3TestCaseMixin, TransactionTestCase):
    def setUp(self):
        super(TestJSONMetadata, self).setUp()
        self.group, _ = Group.objects.get_or_create(name='Hydroshare Author')
        self.user = hydroshare.create_account(
            '<EMAIL>',
            username='user1',
            first_name='Creator_FirstName',
            last_name='Creator_LastName',
            superuser=False,
            groups=[self.group]
        )

        # Create test files
        self.generic_file = "generic_file.txt"
        self.generic_file_obj = open(self.generic_file, 'w')
        self.generic_file_obj.write("This is a generic file for testing")
        self.generic_file_obj.close()

        # Set paths to GeoRaster test files
        self.raster_file_paths = [
            'hs_core/tests/data/test_resource_metadata_files/geographic_raster/logan1.tif',
            'hs_core/tests/data/test_resource_metadata_files/geographic_raster/logan2.tif',
            'hs_core/tests/data/test_resource_metadata_files/geographic_raster/logan.vrt'
        ]

        # Set path to NetCDF test file
        self.netcdf_file_path = 'hs_core/tests/data/test_resource_metadata_files/netcdf/SWE_time.nc'

        # Set paths to GeoFeature test files
        self.geofeature_file_paths = [
            'hs_core/tests/data/test_resource_metadata_files/geographic_feature/watersheds.shp',
            'hs_core/tests/data/test_resource_metadata_files/geographic_feature/watersheds.shx',
            'hs_core/tests/data/test_resource_metadata_files/geographic_feature/watersheds.dbf',
            'hs_core/tests/data/test_resource_metadata_files/geographic_feature/watersheds.prj',
            'hs_core/tests/data/test_resource_metadata_files/geographic_feature/watersheds.sbx',
            'hs_core/tests/data/test_resource_metadata_files/geographic_feature/watersheds.sbn',
            'hs_core/tests/data/test_resource_metadata_files/geographic_feature/watersheds.cpg'
        ]

        # Set path to TimeSeries test file
        self.timeseries_file_path = 'hs_core/tests/data/test_resource_metadata_files/timeseries/' \
        'ODM2_Multi_Site_One_Variable.sqlite'

        # Set path to RefTimeSeries test file
        self.reftimeseries_file_path = 'hs_core/tests/data/multi_sites_formatted_version1.0.refts.json'

        self.composite_resource = None

    def tearDown(self):
        super(TestJSONMetadata, self).tearDown()
        if self.composite_resource:
            self.composite_resource.delete()
        if os.path.exists(self.generic_file):
            os.remove(self.generic_file)

    def test_resource_metadata_json_generation(self):
        """Test resource-level metadata generation and JSON file saving to S3"""

        # Create a composite resource
        self._create_composite_resource()

        # Add metadata to the resource
        self.composite_resource.metadata.create_element(
            'description', abstract='This is a test resource for JSON metadata')
        self.composite_resource.metadata.create_element('subject', value='Test')
        self.composite_resource.metadata.create_element('subject', value='JSON')
        self.composite_resource.metadata.create_element('subject', value='Metadata')

        # Add spatial coverage
        value_dict = {'east': '56.45678', 'north': '12.6789', 'units': 'Decimal degrees'}
        self.composite_resource.metadata.create_element('coverage', type='point', value=value_dict)

        # Add temporal coverage
        value_dict = {'start': '2020-01-01', 'end': '2020-12-31'}
        self.composite_resource.metadata.create_element('coverage', type='period', value=value_dict)

        # add few creators
        self.composite_resource.metadata.create_element('creator', name='John Smith')
        self.composite_resource.metadata.create_element('creator', name='Jane Doe')

        # create few contributors
        self.composite_resource.metadata.create_element('contributor', name='Joe Bloggs')
        self.composite_resource.metadata.create_element('contributor', name='Lisa Smith')

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        json_file_path = os.path.join(
            self.composite_resource.file_path,
            AggregationMetaFilePath.METADATA_JSON_FILE_NAME
        )
        # check the resource metadata json file path
        self.assertEqual(self.composite_resource.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test JSON Metadata')
        self.assertEqual(metadata_dict['description'], 'This is a test resource for JSON metadata')
        self.assertIn('Test', metadata_dict['keywords'])
        self.assertIn('JSON', metadata_dict['keywords'])
        self.assertIn('Metadata', metadata_dict['keywords'])

        # test creators and contributors - check for creator ordering
        self.assertEqual(len(metadata_dict['creator']), 3)
        res_creator_user = metadata_dict['creator'][0]
        john_smith = metadata_dict['creator'][1]
        jane_doe = metadata_dict['creator'][2]
        self.assertEqual(res_creator_user['name'], 'Creator_LastName, Creator_FirstName')
        self.assertEqual(john_smith['name'], 'John Smith')
        self.assertEqual(jane_doe['name'], 'Jane Doe')
        self.assertEqual(res_creator_user["hsterms:order"], 1)
        self.assertEqual(john_smith["hsterms:order"], 2)
        self.assertEqual(jane_doe["hsterms:order"], 3)

        self.assertEqual(len(metadata_dict['contributor']), 2)
        self.assertEqual(metadata_dict['contributor'][0]['name'], 'Joe Bloggs')
        self.assertEqual(metadata_dict['contributor'][1]['name'], 'Lisa Smith')

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoCoordinates')
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['latitude'], 12.6789)
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['longitude'], 56.45678)

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2020-01-01')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2020-12-31')

    def test_generic_file_metadata_json_generation_root(self):
        """Test metadata generation of a single file aggregation (generic logical file) at root level"""

        # Create a composite resource
        self._create_composite_resource()

        # Add a file to the resource
        self._add_generic_file_to_resource()

        # Set file to generic logical file type (create aggregation)
        res_file = self.composite_resource.files.first()
        GenericLogicalFile.set_file_type(self.composite_resource, self.user, res_file.id)

        # Get the logical file
        res_file = self.composite_resource.files.first()
        logical_file = res_file.logical_file

        # Add metadata to the logical file
        logical_file.dataset_name = "Test Generic File"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['Generic', 'File', 'Test']

        # Add spatial coverage
        value_dict = {'east': '56.45678', 'north': '12.6789', 'units': 'Decimal degrees'}
        logical_file.metadata.create_element('coverage', type='point', value=value_dict)

        # Add temporal coverage
        value_dict = {'start': '2020-01-01', 'end': '2020-12-31'}
        logical_file.metadata.create_element('coverage', type='period', value=value_dict)

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the file path
        json_file_path = res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test Generic File')
        self.assertIn('Generic', metadata_dict['keywords'])
        self.assertIn('File', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoCoordinates')
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['latitude'], 12.6789)
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['longitude'], 56.45678)

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2020-01-01')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2020-12-31')

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'generic_file.txt')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check generic isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart generic
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_generic_file_metadata_json_generation_folder(self):
        """Test metadata generation of a single file aggregation (generic logical file) in a folder"""

        # Create a composite resource
        self._create_composite_resource()

        # Create a folder
        folder_path = 'test_folder'
        ResourceFile.create_folder(self.composite_resource, folder_path)

        # Add a file to the folder
        self._add_generic_file_to_resource(folder=folder_path)

        # Set file to generic logical file type (create aggregation)
        res_file = self.composite_resource.files.first()
        GenericLogicalFile.set_file_type(self.composite_resource, self.user, res_file.id)

        # Get the logical file
        res_file = self.composite_resource.files.first()
        logical_file = res_file.logical_file

        # Add metadata to the logical file
        logical_file.dataset_name = "Test Generic File in Folder"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['Generic', 'Folder', 'Test']

        # Add spatial coverage
        value_dict = {'east': '56.45678', 'north': '12.6789', 'units': 'Decimal degrees'}
        logical_file.metadata.create_element('coverage', type='point', value=value_dict)

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the file path
        res_file = self.composite_resource.files.first()
        json_file_path = res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test Generic File in Folder')
        self.assertIn('Generic', metadata_dict['keywords'])
        self.assertIn('Folder', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoCoordinates')
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['latitude'], 12.6789)
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['longitude'], 56.45678)

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'generic_file.txt')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check generic isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart generic
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_georaster_file_metadata_json_generation_root(self):
        """Test metadata generation of a GeoRaster logical file at root level"""

        # Create a composite resource
        self._create_composite_resource()

        # Add TIF and VRT files to the resource
        tif_res_file = self._add_raster_files_to_resource()

        # Set file to GeoRaster logical file type (create aggregation)
        # Use the tif file for creating the aggregation
        GeoRasterLogicalFile.set_file_type(self.composite_resource, self.user, tif_res_file.id)

        # Get the logical file
        logical_files = list(self.composite_resource.logical_files)
        self.assertEqual(len(logical_files), 1)
        logical_file = logical_files[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test GeoRaster File"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['GeoRaster', 'TIF', 'Test']

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the vrt file path
        for res_file in logical_file.files.all():
            if res_file.short_path.endswith('.vrt'):
                vrt_res_file = res_file
                break
        json_file_path = vrt_res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test GeoRaster File')
        self.assertIn('GeoRaster', metadata_dict['keywords'])
        self.assertIn('TIF', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoShape')
        self.assertIn('box', metadata_dict['spatialCoverage']['geo'])

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        # GeoRaster has multiple files (tif and vrt)
        self.assertEqual(len(associated_media), 3)
        # Check that each file is represented correctly
        for media_object in associated_media:
            self.assertEqual(media_object['@type'], 'MediaObject')
            self.assertIn(media_object['name'], ['logan1.tif', 'logan2.tif', 'logan.vrt'])
            self.assertIn('contentUrl', media_object)
            self.assertIn('contentSize', media_object)
            self.assertIn('encodingFormat', media_object)
            self.assertIn('sha256', media_object)

        # Check georaster isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart georaster
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_georaster_file_metadata_json_generation_folder(self):
        """Test metadata generation of a GeoRaster logical file in a folder"""

        # Create a composite resource
        self._create_composite_resource()

        # Create a folder
        folder_path = 'raster_folder'
        ResourceFile.create_folder(self.composite_resource, folder_path)

        # Add TIF and VRT files to the folder
        tif_res_file = self._add_raster_files_to_resource(folder=folder_path)

        # Set file to GeoRaster logical file type (create aggregation)
        # Use the tif file for creating the aggregation
        GeoRasterLogicalFile.set_file_type(self.composite_resource, self.user, tif_res_file.id)

        # Get the logical file - use composite_resource.logical_files property
        logical_files = list(self.composite_resource.logical_files)
        self.assertEqual(len(logical_files), 1)
        logical_file = logical_files[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test GeoRaster File in Folder"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['GeoRaster', 'Folder', 'Test']

        # Add temporal coverage
        value_dict = {'start': '2020-01-01', 'end': '2020-12-31'}
        logical_file.metadata.create_element('coverage', type='period', value=value_dict)

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the vrt file path
        for res_file in logical_file.files.all():
            if res_file.short_path.endswith('.vrt'):
                vrt_res_file = res_file
                break
        json_file_path = vrt_res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test GeoRaster File in Folder')
        self.assertIn('GeoRaster', metadata_dict['keywords'])
        self.assertIn('Folder', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoShape')
        self.assertIn('box', metadata_dict['spatialCoverage']['geo'])

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2020-01-01')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2020-12-31')

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        # GeoRaster has multiple files (tif and vrt)
        self.assertEqual(len(associated_media), 3)
        # Check that each file is represented correctly
        for media_object in associated_media:
            self.assertEqual(media_object['@type'], 'MediaObject')
            self.assertIn(media_object['name'], ['logan1.tif', 'logan2.tif', 'logan.vrt'])
            self.assertIn('contentUrl', media_object)
            self.assertIn('contentSize', media_object)
            self.assertIn('encodingFormat', media_object)
            self.assertIn('sha256', media_object)

        # Check georaster isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart georaster
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_netcdf_file_metadata_json_generation_root(self):
        """Test metadata generation of a NetCDF logical file at root level"""

        # Create a composite resource
        self._create_composite_resource()

        # Add NetCDF file to the resource
        nc_res_file = self._add_netcdf_file_to_resource()

        # Set file to NetCDF logical file type (create aggregation)
        NetCDFLogicalFile.set_file_type(self.composite_resource, self.user, nc_res_file.id)

        # Get the logical file
        logical_files = list(self.composite_resource.logical_files)
        self.assertEqual(len(logical_files), 1)
        logical_file = logical_files[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test NetCDF File"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['NetCDF', 'Climate', 'Test']
        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the nc file path
        json_file_path = nc_res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test NetCDF File')
        self.assertIn('NetCDF', metadata_dict['keywords'])
        self.assertIn('Climate', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoShape')
        self.assertIn('box', metadata_dict['spatialCoverage']['geo'])

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2008-10-01 00:00:00')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2009-06-30 21:00:00')

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 2)
        res_files = ["SWE_time.nc", "SWE_time_header_info.txt"]
        for media_object in associated_media:
            self.assertEqual(media_object['@type'], 'MediaObject')
            self.assertIn(media_object['name'], res_files)
            self.assertIn('contentUrl', media_object)
            self.assertIn('contentSize', media_object)
            self.assertIn('encodingFormat', media_object)
            self.assertIn('sha256', media_object)

        # Check netcdf isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart netcdf
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_netcdf_file_metadata_json_generation_folder(self):
        """Test metadata generation of a NetCDF logical file in a folder"""

        # Create a composite resource
        self._create_composite_resource()

        # Create a folder
        folder_path = 'netcdf_folder'
        ResourceFile.create_folder(self.composite_resource, folder_path)

        # Add NetCDF file to the folder
        nc_res_file = self._add_netcdf_file_to_resource(folder=folder_path)

        # Set file to NetCDF logical file type (create aggregation)
        NetCDFLogicalFile.set_file_type(self.composite_resource, self.user, nc_res_file.id)

        # Get the logical file
        logical_files = list(self.composite_resource.logical_files)
        self.assertEqual(len(logical_files), 1)
        logical_file = logical_files[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test NetCDF File in Folder"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['NetCDF', 'Folder', 'Test']

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the nc file path
        json_file_path = nc_res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test NetCDF File in Folder')
        self.assertIn('NetCDF', metadata_dict['keywords'])
        self.assertIn('Folder', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoShape')
        self.assertIn('box', metadata_dict['spatialCoverage']['geo'])

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2008-10-01 00:00:00')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2009-06-30 21:00:00')

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 2)
        res_files = ["SWE_time.nc", "SWE_time_header_info.txt"]
        for media_object in associated_media:
            self.assertEqual(media_object['@type'], 'MediaObject')
            self.assertIn(media_object['name'], res_files)
            self.assertIn('contentUrl', media_object)
            self.assertIn('contentSize', media_object)
            self.assertIn('encodingFormat', media_object)
            self.assertIn('sha256', media_object)

        # Check netcdf isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart netcdf
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_geofeature_file_metadata_json_generation_root(self):
        """Test metadata generation of a GeoFeature logical file at root level"""

        # Create a composite resource
        self._create_composite_resource()

        # Add shapefile files to the resource
        shp_res_file = self._add_geofeature_files_to_resource()

        # Set file to GeoFeature logical file type (create aggregation)
        # Use the shp file for creating the aggregation
        GeoFeatureLogicalFile.set_file_type(self.composite_resource, self.user, shp_res_file.id)

        # Get the logical file
        logical_files = list(self.composite_resource.logical_files)
        self.assertEqual(len(logical_files), 1)
        logical_file = logical_files[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test GeoFeature File"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['GeoFeature', 'Shapefile', 'Test']

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the shp file path
        json_file_path = shp_res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test GeoFeature File')
        self.assertIn('GeoFeature', metadata_dict['keywords'])
        self.assertIn('Shapefile', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoShape')
        self.assertIn('box', metadata_dict['spatialCoverage']['geo'])

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        # Check that each file is represented correctly
        geofeature_file_names = [os.path.basename(f) for f in self.geofeature_file_paths]
        self.assertEqual(len(associated_media), len(geofeature_file_names))
        for media_object in associated_media:
            self.assertEqual(media_object['@type'], 'MediaObject')
            self.assertIn(media_object['name'], geofeature_file_names)
            self.assertIn('contentUrl', media_object)
            self.assertIn('contentSize', media_object)
            self.assertIn('encodingFormat', media_object)
            self.assertIn('sha256', media_object)

        # Check geofeature isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart geofeature
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_geofeature_file_metadata_json_generation_folder(self):
        """Test metadata generation of a GeoFeature logical file in a folder"""

        # Create a composite resource
        self._create_composite_resource()

        # Create a folder
        folder_path = 'geofeature_folder'
        ResourceFile.create_folder(self.composite_resource, folder_path)

        # Add shapefile files to the folder
        shp_res_file = self._add_geofeature_files_to_resource(folder=folder_path)

        # Set file to GeoFeature logical file type (create aggregation)
        # Use the shp file for creating the aggregation
        GeoFeatureLogicalFile.set_file_type(self.composite_resource, self.user, shp_res_file.id)

        # Get the logical file
        logical_files = list(self.composite_resource.logical_files)
        self.assertEqual(len(logical_files), 1)
        logical_file = logical_files[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test GeoFeature File in Folder"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['GeoFeature', 'Folder', 'Test']

        # Add temporal coverage
        value_dict = {'start': '2020-01-01', 'end': '2020-12-31'}
        logical_file.metadata.create_element('coverage', type='period', value=value_dict)

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the shp file path
        json_file_path = shp_res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test GeoFeature File in Folder')
        self.assertIn('GeoFeature', metadata_dict['keywords'])
        self.assertIn('Folder', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoShape')
        self.assertIn('box', metadata_dict['spatialCoverage']['geo'])

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2020-01-01')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2020-12-31')

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        geofeature_file_names = [os.path.basename(f) for f in self.geofeature_file_paths]
        self.assertEqual(len(associated_media), len(geofeature_file_names))
        for media_object in associated_media:
            self.assertEqual(media_object['@type'], 'MediaObject')
            self.assertIn(media_object['name'], geofeature_file_names)
            self.assertIn('contentUrl', media_object)
            self.assertIn('contentSize', media_object)
            self.assertIn('encodingFormat', media_object)
            self.assertIn('sha256', media_object)

        # Check geofeature isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart geofeature
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_timeseries_file_metadata_json_generation_root(self):
        """Test metadata generation of a TimeSeries logical file at root level"""

        # Create a composite resource
        self._create_composite_resource()

        # Add SQLite file to the resource
        sqlite_res_file = self._add_timeseries_file_to_resource()

        # Set file to TimeSeries logical file type (create aggregation)
        TimeSeriesLogicalFile.set_file_type(self.composite_resource, self.user, sqlite_res_file.id)

        # Get the logical file
        logical_files = list(self.composite_resource.logical_files)
        self.assertEqual(len(logical_files), 1)
        logical_file = logical_files[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test TimeSeries File"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['TimeSeries', 'SQLite', 'Test']

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the sqlite file path
        json_file_path = sqlite_res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test TimeSeries File')
        self.assertIn('TimeSeries', metadata_dict['keywords'])
        self.assertIn('SQLite', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoShape')
        self.assertIn('box', metadata_dict['spatialCoverage']['geo'])

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'ODM2_Multi_Site_One_Variable.sqlite')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check timeseries isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart timeseries
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_timeseries_file_metadata_json_generation_folder(self):
        """Test metadata generation of a TimeSeries logical file in a folder"""

        # Create a composite resource
        self._create_composite_resource()

        # Create a folder
        folder_path = 'timeseries_folder'
        ResourceFile.create_folder(self.composite_resource, folder_path)

        # Add SQLite file to the folder
        sqlite_res_file = self._add_timeseries_file_to_resource(folder=folder_path)

        # Set file to TimeSeries logical file type (create aggregation)
        TimeSeriesLogicalFile.set_file_type(self.composite_resource, self.user, sqlite_res_file.id)

        # Get the logical file
        logical_files = list(self.composite_resource.logical_files)
        self.assertEqual(len(logical_files), 1)
        logical_file = logical_files[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test TimeSeries File in Folder"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['TimeSeries', 'Folder', 'Test']

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the sqlite file path
        json_file_path = sqlite_res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test TimeSeries File in Folder')
        self.assertIn('TimeSeries', metadata_dict['keywords'])
        self.assertIn('Folder', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoShape')
        self.assertIn('box', metadata_dict['spatialCoverage']['geo'])

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2008-01-01 00:00:00')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2008-01-30 23:30:00')

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'ODM2_Multi_Site_One_Variable.sqlite')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check timeseries isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart timeseries
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_fileset_metadata_json_generation(self):
        """Test metadata generation of a FileSet logical file"""

        # Create a composite resource
        self._create_composite_resource()

        # Create a folder
        folder_path = 'fileset_folder'
        ResourceFile.create_folder(self.composite_resource, folder_path)

        # Add a file to the folder
        self._add_generic_file_to_resource(folder=folder_path)

        # Set folder to FileSet logical file type (create aggregation)
        FileSetLogicalFile.set_file_type(self.composite_resource, self.user, folder_path=folder_path)

        # Get the logical file
        logical_file = self.composite_resource.get_logical_files("FileSetLogicalFile")[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test FileSet"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['FileSet', 'Folder', 'Test']

        # Add spatial coverage
        value_dict = {'east': '56.45678', 'north': '12.6789', 'units': 'Decimal degrees'}
        logical_file.metadata.create_element('coverage', type='point', value=value_dict)

        # Add temporal coverage
        value_dict = {'start': '2020-01-01', 'end': '2020-12-31'}
        logical_file.metadata.create_element('coverage', type='period', value=value_dict)

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON metadata file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the folder path
        json_file_path = os.path.join(
            self.composite_resource.file_path,
            folder_path,
            AggregationMetaFilePath.METADATA_JSON_FILE_NAME
        )
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON metadata file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test FileSet')
        self.assertIn('FileSet', metadata_dict['keywords'])
        self.assertIn('Folder', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoCoordinates')
        self.assertIn('latitude', metadata_dict['spatialCoverage']['geo'])
        self.assertIn('longitude', metadata_dict['spatialCoverage']['geo'])

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2020-01-01')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2020-12-31')

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia for FileSet
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'generic_file.txt')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check fileset isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart fileset
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_fileset_with_nested_generic_file_metadata_json_generation(self):
        """Test metadata generation of a FileSet logical file containing a Generic logical file"""

        # Create a composite resource
        self._create_composite_resource()

        # Create a folder
        folder_path = 'fileset_with_generic'
        ResourceFile.create_folder(self.composite_resource, folder_path)

        # Add a file to the folder
        res_file = self._add_generic_file_to_resource(folder=folder_path)

        # Set file to Generic logical file type (create aggregation)
        GenericLogicalFile.set_file_type(self.composite_resource, self.user, res_file.id)

        # Set folder to FileSet logical file type (create aggregation)
        FileSetLogicalFile.set_file_type(self.composite_resource, self.user, folder_path=folder_path)

        # Get the logical files
        fileset_logical_file = self.composite_resource.get_logical_files("FileSetLogicalFile")[0]
        generic_logical_file = self.composite_resource.get_logical_files("GenericLogicalFile")[0]

        # Add metadata to the FileSet logical file
        fileset_logical_file.dataset_name = "Test FileSet with Generic"
        fileset_logical_file.save()

        # Add keywords to FileSet
        fileset_logical_file.metadata.keywords = ['FileSet', 'Nested', 'Test']

        # Add spatial coverage to FileSet
        value_dict = {'east': '56.45678', 'north': '12.6789', 'units': 'Decimal degrees'}
        fileset_logical_file.metadata.create_element('coverage', type='point', value=value_dict)

        # Add extra metadata to FileSet
        fileset_logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        fileset_logical_file.metadata.save()

        # Add metadata to the Generic logical file
        generic_logical_file.dataset_name = "Test Generic in FileSet"
        generic_logical_file.save()

        # Add keywords to Generic
        generic_logical_file.metadata.keywords = ['Generic', 'Nested', 'Test']

        # Add extra metadata to Generic
        generic_logical_file.metadata.extra_metadata = {'key3': 'value3', 'key4': 'value4'}
        generic_logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the FileSet JSON metadata file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the metadata json file path based on the folder path for FileSet
        fs_json_file_path = os.path.join(
            self.composite_resource.file_path,
            folder_path,
            AggregationMetaFilePath.METADATA_JSON_FILE_NAME
        )
        # check the fileset metadata json file path
        self.assertEqual(fileset_logical_file.metadata_json_file_path, fs_json_file_path)
        self.assertTrue(istorage.exists(fs_json_file_path), f"JSON metadata file not found at {fs_json_file_path}")

        # Check if the Generic JSON metadata file exists in S3
        # compute the metadata json file path based on the file path for Generic
        generic_json_file_path = res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the generic metadata json file path
        self.assertEqual(generic_logical_file.metadata_json_file_path, generic_json_file_path)
        self.assertTrue(istorage.exists(generic_json_file_path), f"JSON metadata file not found at {generic_json_file_path}")

        # Verify the content of the FileSet JSON metadata file
        json_content = istorage.open(fs_json_file_path).read().decode('utf-8')
        fileset_metadata_dict = json.loads(json_content)

        # Check basic FileSet metadata
        self.assertEqual(fileset_metadata_dict['name'], 'Test FileSet with Generic')
        self.assertIn('FileSet', fileset_metadata_dict['keywords'])
        self.assertIn('Nested', fileset_metadata_dict['keywords'])
        self.assertIn('Test', fileset_metadata_dict['keywords'])

        # Check spatial coverage in FileSet
        self.assertIn('spatialCoverage', fileset_metadata_dict)
        self.assertEqual(fileset_metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(fileset_metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(fileset_metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', fileset_metadata_dict['spatialCoverage'])
        self.assertEqual(fileset_metadata_dict['spatialCoverage']['geo']['@type'], 'GeoCoordinates')
        self.assertIn('latitude', fileset_metadata_dict['spatialCoverage']['geo'])
        self.assertIn('longitude', fileset_metadata_dict['spatialCoverage']['geo'])

        # Check FileSet isPartOf resource
        self.assertIn('isPartOf', fileset_metadata_dict)
        self.assertEqual(len(fileset_metadata_dict['isPartOf']), 1)
        self.assertEqual(fileset_metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check generic isPartOf FileSet
        json_content = istorage.open(generic_json_file_path).read().decode('utf-8')
        generic_metadata_dict = json.loads(json_content)
        self.assertIn('isPartOf', generic_metadata_dict)
        self.assertEqual(len(generic_metadata_dict['isPartOf']), 1)
        self.assertEqual(generic_metadata_dict['isPartOf'][0], fileset_logical_file.metadata_json_file_url_path)

        # Check FileSet hasPart generic
        self.assertIn('hasPart', fileset_metadata_dict)
        self.assertEqual(len(fileset_metadata_dict['hasPart']), 1)
        self.assertEqual(fileset_metadata_dict['hasPart'][0], generic_logical_file.metadata_json_file_url_path)

        # Check resource hasPart FileSet
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], fileset_logical_file.metadata_json_file_url_path)

        # Check extra metadata in FileSet
        self.assertIn('hsterms:additionalMetadata', fileset_metadata_dict)
        additional_metadata = fileset_metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia in FileSet - it should be missing as there is no file that is directly associated
        # with the FileSet aggregation
        self.assertNotIn('associatedMedia', fileset_metadata_dict)

        # Check associatedMedia in Generic
        self.assertIn('associatedMedia', generic_metadata_dict)
        self.assertEqual(len(generic_metadata_dict['associatedMedia']), 1)
        media_object = generic_metadata_dict['associatedMedia'][0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'generic_file.txt')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Verify the content of the Generic JSON metadata file
        json_content = istorage.open(generic_json_file_path).read().decode('utf-8')
        generic_metadata_dict = json.loads(json_content)

        # Check basic Generic metadata
        self.assertEqual(generic_metadata_dict['name'], 'Test Generic in FileSet')
        self.assertIn('Generic', generic_metadata_dict['keywords'])
        self.assertIn('Nested', generic_metadata_dict['keywords'])
        self.assertIn('Test', generic_metadata_dict['keywords'])

        # Check extra metadata in Generic
        self.assertIn('hsterms:additionalMetadata', generic_metadata_dict)
        additional_metadata = generic_metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia in Generic
        self.assertIn('associatedMedia', generic_metadata_dict)
        associated_media = generic_metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'generic_file.txt')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check that the context is properly set in both files
        self.assertIn('@context', fileset_metadata_dict)
        self.assertIn('https://schema.org/', fileset_metadata_dict['@context'])
        self.assertIn('hsterms', fileset_metadata_dict['@context'][1])

        self.assertIn('@context', generic_metadata_dict)
        self.assertIn('https://schema.org/', generic_metadata_dict['@context'])
        self.assertIn('hsterms', generic_metadata_dict['@context'][1])

    def test_model_program_file_metadata_json_generation_root(self):
        """Test metadata generation of a ModelProgram logical file at root level"""

        # Create a composite resource
        self._create_composite_resource()

        # Add a file to the resource
        res_file = self._add_generic_file_to_resource()

        # Set file to ModelProgram logical file type (create aggregation)
        ModelProgramLogicalFile.set_file_type(self.composite_resource, self.user, res_file.id)

        # Get the logical file
        logical_file = self.composite_resource.get_logical_files("ModelProgramLogicalFile")[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test ModelProgram File"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['ModelProgram', 'File', 'Test']

        # Add version
        logical_file.metadata.version = '1.0.0'

        # Add programming language
        logical_file.metadata.programming_languages = ['Python', 'R']

        # Add operating system
        logical_file.metadata.operating_systems = ['Linux', 'Windows']

        # Add release date
        logical_file.metadata.release_date = '2023-01-01'

        # Add website
        logical_file.metadata.website = 'https://www.google.com'

        # Add code repository
        logical_file.metadata.code_repository = 'https://www.github.com/swat'

        # Add spatial coverage
        value_dict = {'east': '56.45678', 'north': '12.6789', 'units': 'Decimal degrees'}
        logical_file.metadata.create_element('coverage', type='point', value=value_dict)

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON metadata file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the metadata json file path based on the file path for ModelProgram
        json_file_path = res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test ModelProgram File')
        self.assertIn('ModelProgram', metadata_dict['keywords'])
        self.assertIn('File', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check model program specific metadata
        self.assertEqual(metadata_dict['hsterms:modelVersion'], '1.0.0')
        self.assertEqual(metadata_dict['hsterms:modelProgramLanguage'], ['Python', 'R'])
        self.assertEqual(metadata_dict['hsterms:modelOperatingSystem'], ['Linux', 'Windows'])
        self.assertEqual(metadata_dict['hsterms:modelReleaseDate'], '2023-01-01')
        self.assertEqual(metadata_dict['hsterms:modelWebsite'], 'https://www.google.com')
        self.assertEqual(metadata_dict['hsterms:modelCodeRepository'], 'https://www.github.com/swat')

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoCoordinates')
        self.assertIn('latitude', metadata_dict['spatialCoverage']['geo'])
        self.assertIn('longitude', metadata_dict['spatialCoverage']['geo'])

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'generic_file.txt')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check model program isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart model program
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_model_program_folder_metadata_json_generation(self):
        """Test metadata generation of a ModelProgram logical file in a folder"""

        # Create a composite resource
        self._create_composite_resource()

        # Create a folder
        folder_path = 'model_program_folder'
        ResourceFile.create_folder(self.composite_resource, folder_path)

        # Add multiple files to the folder - so that some of the files can be marked as
        # different modelprogram file types
        self._add_raster_files_to_resource(folder=folder_path)

        # Set folder to ModelProgram logical file type (create aggregation)
        ModelProgramLogicalFile.set_file_type(self.composite_resource, self.user, folder_path=folder_path)

        # Get the logical file
        logical_file = self.composite_resource.get_logical_files("ModelProgramLogicalFile")[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test ModelProgram Folder"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['ModelProgram', 'Folder', 'Test']

        # Add version
        logical_file.metadata.version = '2.0.0'

        # Add programming language
        logical_file.metadata.programming_languages = ['Python', 'R']

        # Add operating system
        logical_file.metadata.operating_systems = ['Linux', 'Windows']

        # Add release date
        logical_file.metadata.release_date = '2023-02-01'

        # Add spatial coverage
        value_dict = {'east': '56.45678', 'north': '12.6789', 'units': 'Decimal degrees'}
        logical_file.metadata.create_element('coverage', type='point', value=value_dict)

        # Add temporal coverage
        value_dict = {'start': '2020-01-01', 'end': '2020-12-31'}
        logical_file.metadata.create_element('coverage', type='period', value=value_dict)

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Set model program file types
        for res_file in logical_file.files.all():
            if res_file.short_path.endswith('.vrt'):
                ModelProgramResourceFileType.create(file_type='Software', res_file=res_file,
                                                    mp_metadata=logical_file.metadata)
            elif res_file.short_path.endswith('logan1.tif'):
                ModelProgramResourceFileType.create(file_type='Documentation', res_file=res_file,
                                                    mp_metadata=logical_file.metadata)
            elif res_file.short_path.endswith('logan2.tif'):
                ModelProgramResourceFileType.create(file_type='Release Notes', res_file=res_file,
                                                    mp_metadata=logical_file.metadata)

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON metadata file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the metadata json file path based on the folder path
        json_file_path = os.path.join(
            self.composite_resource.file_path,
            folder_path,
            AggregationMetaFilePath.METADATA_JSON_FILE_NAME
        )

        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test ModelProgram Folder')
        self.assertIn('ModelProgram', metadata_dict['keywords'])
        self.assertIn('Folder', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check model program specific metadata
        self.assertEqual(metadata_dict['hsterms:modelVersion'], '2.0.0')
        self.assertEqual(metadata_dict['hsterms:modelProgramLanguage'], ['Python', 'R'])
        self.assertEqual(metadata_dict['hsterms:modelOperatingSystem'], ['Linux', 'Windows'])
        self.assertEqual(metadata_dict['hsterms:modelReleaseDate'], '2023-02-01')

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoCoordinates')
        self.assertIn('latitude', metadata_dict['spatialCoverage']['geo'])
        self.assertIn('longitude', metadata_dict['spatialCoverage']['geo'])

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2020-01-01')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2020-12-31')

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        # ModelProgram folder has multiple files (tif and vrt)
        self.assertEqual(len(associated_media), 3)
        # Check that each file is represented correctly
        for media_object in associated_media:
            self.assertEqual(media_object['@type'], 'MediaObject')
            self.assertIn(media_object['name'], ['logan1.tif', 'logan2.tif', 'logan.vrt'])
            self.assertIn('contentUrl', media_object)
            self.assertIn('contentSize', media_object)
            self.assertIn('encodingFormat', media_object)
            self.assertIn('sha256', media_object)

        # Check model program file types
        self.assertIn('hsterms:modelProgramFileType', metadata_dict)
        mp_file_types = metadata_dict['hsterms:modelProgramFileType']
        self.assertEqual(len(mp_file_types), 3)
        file_path = f"data/contents/{folder_path}"
        # check the expected keys are present
        mp_file_type_keys = []
        for mp_file_type in mp_file_types:
            mp_file_type_keys.append(list(mp_file_type.keys())[0])

        self.assertIn('hsterms:modelDocumentation', mp_file_type_keys)
        self.assertIn('hsterms:modelReleaseNotes', mp_file_type_keys)
        self.assertIn('hsterms:modelSoftware', mp_file_type_keys)
        # since the website url is generated as http://example.com in the test, we need to
        # check for the url has the part staring with 'resource/'
        for mp_file_type in mp_file_types:
            for key, value in mp_file_type.items():
                if key == 'hsterms:modelDocumentation':
                    path_to_check = f"resource/{self.composite_resource.short_id}/{file_path}/logan1.tif"
                    self.assertIn(path_to_check, value)
                elif key == 'hsterms:modelReleaseNotes':
                    path_to_check = f"resource/{self.composite_resource.short_id}/{file_path}/logan2.tif"
                    self.assertIn(path_to_check, value)
                elif key == 'hsterms:modelSoftware':
                    path_to_check = f"resource/{self.composite_resource.short_id}/{file_path}/logan.vrt"
                    self.assertIn(path_to_check, value)

        # Check model program isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart model program
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_model_instance_file_metadata_json_generation_root(self):
        """Test metadata generation of a ModelInstance logical file at root level"""

        # Create a composite resource
        self._create_composite_resource()

        # First create a ModelProgram logical file to link with the ModelInstance
        # Create a folder for the ModelProgram
        model_program_folder = 'model_program_folder'
        ResourceFile.create_folder(self.composite_resource, model_program_folder)

        # Add a file to the folder for ModelProgram
        self._add_generic_file_to_resource(folder=model_program_folder)

        # Set folder to ModelProgram logical file type (create aggregation)
        ModelProgramLogicalFile.set_file_type(self.composite_resource, self.user, folder_path=model_program_folder)

        # Get the ModelProgram logical file
        model_program_logical_file = self.composite_resource.get_logical_files("ModelProgramLogicalFile")[0]

        # Add metadata to the ModelProgram logical file
        model_program_logical_file.dataset_name = "ModelProgram for ExecutedBy"
        model_program_logical_file.save()

        # Set JSON schema for model program aggregation
        schema_file_path = 'pytest/assets/mi_schema.json'
        with open(schema_file_path, 'r') as file_obj:
            meta_schema_json = file_obj.read()
        self.assertTrue(len(meta_schema_json) > 0)
        meta_schema_json = json.loads(meta_schema_json)
        model_program_logical_file.metadata.metadata_schema_json = meta_schema_json
        model_program_logical_file.metadata.save()

        # Now create a ModelInstance logical file
        # Create a different file for ModelInstance
        model_instance_file_name = "model_instance_file.txt"
        model_instance_file_obj = open(model_instance_file_name, 'w')
        model_instance_file_obj.write("This is a model instance file for testing")
        model_instance_file_obj.close()

        # Upload the model instance file to the resource
        model_instance_file_to_upload = UploadedFile(file=open(model_instance_file_name, 'rb'),
                                                    name=model_instance_file_name)
        model_instance_file = add_file_to_resource(
            self.composite_resource, model_instance_file_to_upload, folder='', check_target_folder=True
        )

        # Clean up the temporary file
        os.remove(model_instance_file_name)

        # Set file to ModelInstance logical file type (create aggregation)
        ModelInstanceLogicalFile.set_file_type(self.composite_resource, self.user, model_instance_file.id)

        # Get the ModelInstance logical file
        model_instance_logical_file = self.composite_resource.get_logical_files("ModelInstanceLogicalFile")[0]

        # Add metadata to the ModelInstance logical file
        model_instance_logical_file.dataset_name = "Test Model Instance Aggregation"
        model_instance_logical_file.save()

        # Add keywords
        model_instance_logical_file.metadata.keywords = ['ModelInstance', 'File', 'Test']

        # Set executed_by to link to the ModelProgram
        model_instance_logical_file.metadata.executed_by = model_program_logical_file

        # Add spatial coverage
        value_dict = {'east': '56.45678', 'north': '12.6789', 'units': 'Decimal degrees'}
        model_instance_logical_file.metadata.create_element('coverage', type='point', value=value_dict)

        # Add extra metadata
        model_instance_logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        # Add has model output
        model_instance_logical_file.metadata.has_model_output = True
        model_instance_logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the ModelInstance JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the metadata json file path based on the file path for ModelInstance
        json_file_path = model_instance_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(model_instance_logical_file.metadata_json_file_path, json_file_path)
        err_msg = f"ModelInstance JSON metadata file not found at {json_file_path}"
        self.assertTrue(istorage.exists(json_file_path), err_msg)

        # Verify the content of the ModelInstance JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test Model Instance Aggregation')
        self.assertIn('ModelInstance', metadata_dict['keywords'])
        self.assertIn('File', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check model instance specific metadata - executed_by should reference the model program
        self.assertIn('hsterms:executedByModelProgram', metadata_dict)
        executed_by = metadata_dict['hsterms:executedByModelProgram']
        # get the json metadata url file path for the model program aggregation
        model_program_json_file_path = model_program_logical_file.metadata_json_file_url_path
        self.assertEqual(executed_by, model_program_json_file_path)

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoCoordinates')
        self.assertIn('latitude', metadata_dict['spatialCoverage']['geo'])
        self.assertIn('longitude', metadata_dict['spatialCoverage']['geo'])

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia for model instance
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'model_instance_file.txt')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check hasPart model instance
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_model_instance_folder_metadata_json_generation(self):
        """Test metadata generation of a ModelInstance logical file in a folder"""

        # Create a composite resource
        self._create_composite_resource()

        # First create a ModelProgram logical file to link with the ModelInstance

        # Add a file to to the root of resource directory
        res_file = self._add_generic_file_to_resource()

        # Set file to ModelProgram logical file type (create aggregation)
        ModelProgramLogicalFile.set_file_type(self.composite_resource, self.user, res_file.id)

        # Get the ModelProgram logical file
        model_program_logical_file = self.composite_resource.get_logical_files("ModelProgramLogicalFile")[0]

        # Add metadata to the ModelProgram logical file
        model_program_logical_file.dataset_name = "Test ModelProgram ExecutedBy"
        model_program_logical_file.save()

        # Set JSON schema for model program aggregation
        schema_file_path = 'pytest/assets/mi_schema.json'
        with open(schema_file_path, 'r') as file_obj:
            meta_schema_json = file_obj.read()
        self.assertTrue(len(meta_schema_json) > 0)
        meta_schema_json = json.loads(meta_schema_json)
        model_program_logical_file.metadata.metadata_schema_json = meta_schema_json
        model_program_logical_file.metadata.save()

        # Now create a ModelInstance logical file in a folder
        # Create a folder
        folder_path = 'model_instance_folder'
        ResourceFile.create_folder(self.composite_resource, folder_path)

        # Add a file to the folder - add the vrt file
        vrt_file_path = 'hs_core/tests/data/test_resource_metadata_files/geographic_raster/logan.vrt'
        self._add_file_to_resource(file_to_add=vrt_file_path, folder=folder_path)

        # Set folder to ModelInstance logical file type (create aggregation)
        ModelInstanceLogicalFile.set_file_type(self.composite_resource, self.user, folder_path=folder_path)

        # Get the ModelInstance logical file
        model_instance_logical_file = self.composite_resource.get_logical_files("ModelInstanceLogicalFile")[0]

        # Add metadata to the ModelInstance logical file
        model_instance_logical_file.dataset_name = "Test ModelInstance Folder based aggregation"
        model_instance_logical_file.save()

        # Add keywords
        model_instance_logical_file.metadata.keywords = ['ModelInstance', 'Folder', 'Test']

        # Set executed_by to link to the ModelProgram
        model_instance_logical_file.metadata.executed_by = model_program_logical_file

        # Add spatial coverage
        value_dict = {'east': '56.45678', 'north': '12.6789', 'units': 'Decimal degrees'}
        model_instance_logical_file.metadata.create_element('coverage', type='point', value=value_dict)

        # Add temporal coverage
        value_dict = {'start': '2020-01-01', 'end': '2020-12-31'}
        model_instance_logical_file.metadata.create_element('coverage', type='period', value=value_dict)

        # Add has model output
        model_instance_logical_file.metadata.has_model_output = False
        model_instance_logical_file.metadata.save()

        # Add extra metadata
        model_instance_logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}

        # Set metadata JSON from the content of the file
        schema_file_path = 'pytest/assets/mi_metadata.json'
        with open(schema_file_path, 'r') as file_obj:
            meta_json = file_obj.read()
        self.assertTrue(len(meta_json) > 0)
        meta_json = json.loads(meta_json)
        model_instance_logical_file.metadata.metadata_json = meta_json
        model_instance_logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the ModelInstance  metadata JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the metadata json file path based on the folder path for ModelInstance
        json_file_path = os.path.join(
            self.composite_resource.file_path,
            folder_path,
            AggregationMetaFilePath.METADATA_JSON_FILE_NAME
        )
        # check the logical file metadata json file path
        self.assertEqual(model_instance_logical_file.metadata_json_file_path, json_file_path)
        err_msg = f"ModelInstance JSON metadata file not found at {json_file_path}"
        self.assertTrue(istorage.exists(json_file_path), err_msg)

        # Check if the ModelInstance  metadata values JSON file exists in S3
        json_values_file_path = model_instance_logical_file.schema_values_file_path
        err_msg = f"ModelInstance JSON metadata values file not found at {json_values_file_path}"
        self.assertTrue(istorage.exists(json_values_file_path), err_msg)

        # Verify the content of the ModelInstance JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test ModelInstance Folder based aggregation')
        self.assertIn('ModelInstance', metadata_dict['keywords'])
        self.assertIn('Folder', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check model instance specific metadata - executed_by should reference the model program
        self.assertIn('hsterms:executedByModelProgram', metadata_dict)
        executed_by = metadata_dict['hsterms:executedByModelProgram']
        # get the json metadata url file path for the model program aggregation
        model_program_json_file_path = model_program_logical_file.metadata_json_file_url_path
        self.assertEqual(executed_by, model_program_json_file_path)

        # Check model output
        self.assertIn('hsterms:includesModelOutput', metadata_dict)
        self.assertFalse(metadata_dict['hsterms:includesModelOutput'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'WGS 84 EPSG:4326')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoCoordinates')
        self.assertIn('latitude', metadata_dict['spatialCoverage']['geo'])
        self.assertIn('longitude', metadata_dict['spatialCoverage']['geo'])

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2020-01-01')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2020-12-31')

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia for model instance
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'logan.vrt')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check model instance isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart model instance and model program
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 2)
        self.assertIn(model_instance_logical_file.metadata_json_file_url_path, resource_metadata_dict['hasPart'])
        self.assertIn(model_program_logical_file.metadata_json_file_url_path, resource_metadata_dict['hasPart'])

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_reftimeseries_file_metadata_json_generation_root(self):
        """Test metadata generation of a RefTimeSeries logical file at root level"""

        # Create a composite resource
        self._create_composite_resource()

        # Add RefTimeSeries file to the resource
        ref_ts_res_file = self._add_reftimeseries_file_to_resource()

        # Set file to RefTimeSeries logical file type (create aggregation)
        RefTimeseriesLogicalFile.set_file_type(self.composite_resource, self.user, ref_ts_res_file.id)

        # Get the logical file
        logical_file = self.composite_resource.get_logical_files("RefTimeseriesLogicalFile")[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test RefTimeSeries Aggregation"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['RefTimeSeries', 'File', 'Test']

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the file path
        json_file_path = ref_ts_res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test RefTimeSeries Aggregation')
        self.assertIn('RefTimeSeries', metadata_dict['keywords'])
        self.assertIn('File', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'Unknown')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoShape')
        self.assertIn('box', metadata_dict['spatialCoverage']['geo'])

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2016-04-06T00:00:00')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2017-02-09T23:45:00')

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'multi_sites_formatted_version1.0.refts.json')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check hasPart reftimeseries
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_reftimeseries_folder_metadata_json_generation(self):
        """Test metadata generation of a RefTimeSeries logical file in a folder"""

        # Create a composite resource
        self._create_composite_resource()

        # Create a folder
        folder_path = 'reftimeseries_folder'
        ResourceFile.create_folder(self.composite_resource, folder_path)

        # Add a file to the folder
        res_file = self._add_reftimeseries_file_to_resource(folder=folder_path)

        # Set the uploaded file to RefTimeSeries logical file type (create aggregation)
        RefTimeseriesLogicalFile.set_file_type(self.composite_resource, self.user, res_file.id)

        # Get the logical file
        logical_file = self.composite_resource.get_logical_files("RefTimeseriesLogicalFile")[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test RefTimeSeries Aggregation in Folder"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['RefTimeSeries', 'Folder', 'Test']

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the metadata json file path based on the file path
        json_file_path = res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test RefTimeSeries Aggregation in Folder')
        self.assertIn('RefTimeSeries', metadata_dict['keywords'])
        self.assertIn('Folder', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check spatial coverage
        self.assertIn('spatialCoverage', metadata_dict)
        self.assertEqual(metadata_dict['spatialCoverage']['@type'], 'Place')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:projection'], 'Unknown')
        self.assertEqual(metadata_dict['spatialCoverage']['hsterms:units'], 'Decimal degrees')
        self.assertIn('geo', metadata_dict['spatialCoverage'])
        self.assertEqual(metadata_dict['spatialCoverage']['geo']['@type'], 'GeoShape')
        self.assertIn('box', metadata_dict['spatialCoverage']['geo'])

        # Check temporal coverage
        self.assertIn('temporalCoverage', metadata_dict)
        self.assertEqual(metadata_dict['temporalCoverage']['start'], '2016-04-06T00:00:00')
        self.assertEqual(metadata_dict['temporalCoverage']['end'], '2017-02-09T23:45:00')

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'multi_sites_formatted_version1.0.refts.json')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check hasPart reftimeseries
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_csv_file_metadata_json_generation_root(self):
        """Test metadata generation of a CSV logical file at root level"""

        # Create a composite resource
        self._create_composite_resource()

        # Add CSV file to the resource
        csv_res_file = self._add_csv_file_to_resource()

        # Set file to CSV logical file type (create aggregation)
        CSVLogicalFile.set_file_type(self.composite_resource, self.user, csv_res_file.id)

        # Get the logical file
        logical_file = self.composite_resource.get_logical_files("CSVLogicalFile")[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test CSV File"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['CSV', 'File', 'Test']

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the json file path based on the file path
        json_file_path = csv_res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test CSV File')
        self.assertIn('CSV', metadata_dict['keywords'])
        self.assertIn('File', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check CSV specific metadata
        csv_table_schema = metadata_dict['hsterms:csvTableSchema']
        self.assertIn('hsterms:delimiter', csv_table_schema)
        self.assertIn('hsterms:numberOfDataRows', csv_table_schema)
        self.assertIn('hsterms:columns', csv_table_schema)
        self.assertTrue(len(csv_table_schema['hsterms:columns']) > 0)

        # Check a column entry
        column = csv_table_schema['hsterms:columns'][0]
        self.assertIn('hsterms:columnNumber', column)
        self.assertIn('hsterms:dataType', column)

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'csv_with_header_and_data.csv')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check resource hasPart csv
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def test_csv_file_metadata_json_generation_folder(self):
        """Test metadata generation of a CSV logical file in a folder"""

        # Create a composite resource
        self._create_composite_resource()

        # Create a folder
        folder_path = 'csv_folder'
        ResourceFile.create_folder(self.composite_resource, folder_path)

        # Add a file to the folder
        res_file = self._add_csv_file_to_resource(folder=folder_path)

        # Set the uploaded file to CSV logical file type (create aggregation)
        CSVLogicalFile.set_file_type(self.composite_resource, self.user, res_file.id)

        # Get the logical file
        logical_file = self.composite_resource.get_logical_files("CSVLogicalFile")[0]

        # Add metadata to the logical file
        logical_file.dataset_name = "Test CSV File in Folder"
        logical_file.save()

        # Add keywords
        logical_file.metadata.keywords = ['CSV', 'Folder', 'Test']

        # Add extra metadata
        logical_file.metadata.extra_metadata = {'key1': 'value1', 'key2': 'value2'}
        logical_file.metadata.save()

        # Generate and save metadata JSON
        save_resource_metadata_json(self.composite_resource)

        # Check if the JSON file exists in S3
        istorage = self.composite_resource.get_s3_storage()
        # compute the metadata json file path based on the file path
        json_file_path = res_file.storage_path + AggregationMetaFilePath.METADATA_JSON_FILE_ENDSWITH
        # check the logical file metadata json file path
        self.assertEqual(logical_file.metadata_json_file_path, json_file_path)
        self.assertTrue(istorage.exists(json_file_path), f"JSON metadata file not found at {json_file_path}")

        # Verify the content of the JSON file
        json_content = istorage.open(json_file_path).read().decode('utf-8')
        metadata_dict = json.loads(json_content)

        # Check basic metadata
        self.assertEqual(metadata_dict['name'], 'Test CSV File in Folder')
        self.assertIn('CSV', metadata_dict['keywords'])
        self.assertIn('Folder', metadata_dict['keywords'])
        self.assertIn('Test', metadata_dict['keywords'])

        # Check CSV specific metadata
        csv_table_schema = metadata_dict['hsterms:csvTableSchema']
        self.assertIn('hsterms:delimiter', csv_table_schema)
        self.assertIn('hsterms:numberOfDataRows', csv_table_schema)
        self.assertIn('hsterms:columns', csv_table_schema)
        self.assertTrue(len(csv_table_schema['hsterms:columns']) > 0)

        # Check a column entry
        column = csv_table_schema['hsterms:columns'][0]
        self.assertIn('hsterms:columnNumber', column)
        self.assertIn('hsterms:dataType', column)

        # Check extra metadata
        self.assertIn('hsterms:additionalMetadata', metadata_dict)
        additional_metadata = metadata_dict['hsterms:additionalMetadata']
        self.assertEqual(len(additional_metadata), 2)

        # Check associatedMedia
        self.assertIn('associatedMedia', metadata_dict)
        associated_media = metadata_dict['associatedMedia']
        self.assertEqual(len(associated_media), 1)
        media_object = associated_media[0]
        self.assertEqual(media_object['@type'], 'MediaObject')
        self.assertEqual(media_object['name'], 'csv_with_header_and_data.csv')
        self.assertIn('contentUrl', media_object)
        self.assertIn('contentSize', media_object)
        self.assertIn('encodingFormat', media_object)
        self.assertIn('sha256', media_object)

        # Check isPartOf resource
        self.assertIn('isPartOf', metadata_dict)
        self.assertEqual(len(metadata_dict['isPartOf']), 1)
        self.assertEqual(metadata_dict['isPartOf'][0], self.composite_resource.metadata_json_file_url_path)

        # Check hasPart csv
        json_content = istorage.open(self.composite_resource.metadata_json_file_path).read().decode('utf-8')
        resource_metadata_dict = json.loads(json_content)
        self.assertIn('hasPart', resource_metadata_dict)
        self.assertEqual(len(resource_metadata_dict['hasPart']), 1)
        self.assertEqual(resource_metadata_dict['hasPart'][0], logical_file.metadata_json_file_url_path)

        # Check that the context is properly set
        self.assertIn('@context', metadata_dict)
        self.assertIn('https://schema.org/', metadata_dict['@context'])
        self.assertIn('hsterms', metadata_dict['@context'][1])

    def _create_composite_resource(self):
        """Helper method to create a composite resource"""
        self.composite_resource = hydroshare.create_resource(
            resource_type='CompositeResource',
            owner=self.user,
            title='Test JSON Metadata'
        )

    def _add_file_to_resource(self, file_to_add, folder=''):
        """Helper method to add a file to the resource"""
        file_to_upload = UploadedFile(file=open(file_to_add, 'rb'),
                                      name=os.path.basename(file_to_add))
        return add_file_to_resource(
            self.composite_resource, file_to_upload, folder=folder, check_target_folder=True
        )

    def _add_generic_file_to_resource(self, folder=''):
        """Helper method to add a generic file to the resource"""
        file_to_upload = UploadedFile(file=open(self.generic_file, 'rb'),
                                      name=os.path.basename(self.generic_file))
        return add_file_to_resource(
            self.composite_resource, file_to_upload, folder=folder, check_target_folder=True
        )

    def _add_raster_files_to_resource(self, folder=''):
        """Helper method to add raster files to the resource"""
        tif_res_file = None
        for file_path in self.raster_file_paths:
            file_to_upload = UploadedFile(file=open(file_path, 'rb'),
                                          name=os.path.basename(file_path))
            res_file = add_file_to_resource(
                self.composite_resource, file_to_upload, folder=folder, check_target_folder=True
            )
            if res_file.short_path.endswith('.tif'):
                tif_res_file = res_file
        return tif_res_file

    def _add_geofeature_files_to_resource(self, folder=''):
        """Helper method to add shapefile files to the resource"""
        shp_res_file = None
        for file_path in self.geofeature_file_paths:
            file_to_upload = UploadedFile(file=open(file_path, 'rb'),
                                          name=os.path.basename(file_path))
            res_file = add_file_to_resource(
                self.composite_resource, file_to_upload, folder=folder, check_target_folder=True
            )
            if res_file.short_path.endswith('.shp'):
                shp_res_file = res_file
        return shp_res_file

    def _add_timeseries_file_to_resource(self, folder=''):
        """Helper method to add a TimeSeries SQLite file to the resource"""
        file_to_upload = UploadedFile(file=open(self.timeseries_file_path, 'rb'),
                                      name=os.path.basename(self.timeseries_file_path))
        return add_file_to_resource(
            self.composite_resource, file_to_upload, folder=folder, check_target_folder=True
        )

    def _add_netcdf_file_to_resource(self, folder=''):
        """Helper method to add a NetCDF file to the resource"""
        file_to_upload = UploadedFile(file=open(self.netcdf_file_path, 'rb'),
                                      name=os.path.basename(self.netcdf_file_path))
        return add_file_to_resource(
            self.composite_resource, file_to_upload, folder=folder, check_target_folder=True
        )

    def _add_reftimeseries_file_to_resource(self, folder=''):
        """Helper method to add a RefTimeSeries file to the resource"""
        file_to_upload = UploadedFile(file=open(self.reftimeseries_file_path, 'rb'),
                                      name=os.path.basename(self.reftimeseries_file_path))
        return add_file_to_resource(
            self.composite_resource, file_to_upload, folder=folder, check_target_folder=True
        )

    def _add_csv_file_to_resource(self, folder=''):
        """Helper method to add a CSV file to the resource"""
        csv_file_path = 'hs_file_types/tests/data/csv_with_header_and_data.csv'
        file_to_upload = UploadedFile(file=open(csv_file_path, 'rb'),
                                      name=os.path.basename(csv_file_path))
        return add_file_to_resource(
            self.composite_resource, file_to_upload, folder=folder, check_target_folder=True
        )
