# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2022-05-20 20:47
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hs_access_control', '0042_communityrequest'),
    ]

    operations = [
        migrations.RenameField(
            model_name='communityrequest',
            old_name='date_created',
            new_name='date_requested',
        ),
        migrations.AddField(
            model_name='communityrequest',
            name='date_processed',
            field=models.DateTimeField(editable=False, null=True),
        ),
        migrations.AlterField(
            model_name='community',
            name='closed',
            field=models.BooleanField(default=False, editable=False),
        ),
    ]
