# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2022-02-14 21:32
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hs_access_control', '0033_auto_20220217_2304'),
    ]

    operations = [
        migrations.AddField(
            model_name='resourceaccess',
            name='allow_private_sharing',
            field=models.BooleanField(default=False, help_text='whether to allow anonymous user to access private resource in view mode'),
        ),
    ]
