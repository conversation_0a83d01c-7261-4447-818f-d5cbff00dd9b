# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2022-05-16 13:39
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('hs_access_control', '0041_community_active'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommunityRequest',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField()),
                ('description', models.TextField()),
                ('email', models.TextField(blank=True, null=True)),
                ('url', models.TextField(blank=True, null=True)),
                ('purpose', models.TextField(blank=True, null=True)),
                ('auto_approve', models.BooleanField(default=False, editable=False)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('picture', models.ImageField(blank=True, null=True, upload_to='community')),
                ('closed', models.BooleanField(default=True, editable=False)),
                ('approved', models.NullBooleanField(default=None, editable=False)),
                ('owner', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
