# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2021-10-18 14:40
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('hs_access_control', '0032_auto_20210607_2027'),
    ]

    operations = [
        migrations.AlterField(
            model_name='groupcommunityrequest',
            name='community',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, related_name='invite_gcr_community', to='hs_access_control.Community'),
        ),
        migrations.AlterField(
            model_name='groupcommunityrequest',
            name='community_owner',
            field=models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='invite_gcr_cowner', to=settings.AUTH_USER_MODEL),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='groupcommunityrequest',
            name='group',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, related_name='invite_gcr_group', to='auth.Group'),
        ),
        migrations.AlterField(
            model_name='groupcommunityrequest',
            name='group_owner',
            field=models.ForeignKey(default=None, editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='invite_gcr_gowner', to=settings.AUTH_USER_MODEL),
        ),
    ]
