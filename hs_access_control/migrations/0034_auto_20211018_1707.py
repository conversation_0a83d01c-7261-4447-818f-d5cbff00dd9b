# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2021-10-18 17:07
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('hs_access_control', '0033_auto_20211018_1440'),
    ]

    operations = [
        migrations.AlterField(
            model_name='groupcommunityrequest',
            name='community',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, related_name='invite_c2gcr', to='hs_access_control.Community'),
        ),
        migrations.AlterField(
            model_name='groupcommunityrequest',
            name='community_owner',
            field=models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='invite_co2gcr', to=settings.AUTH_USER_MODEL),
        ),
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name='groupcommunityrequest',
            name='group',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, related_name='invite_g2gcr', to='auth.Group'),
        ),
        migrations.AlterField(
            model_name='groupcommunityrequest',
            name='group_owner',
            field=models.ForeignKey(default=None, editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='invite_go2gcr', to=settings.AUTH_USER_MODEL),
        ),
    ]
