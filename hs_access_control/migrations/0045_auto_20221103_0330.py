# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2022-11-03 03:30
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import sorl.thumbnail.fields
import theme.utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('hs_access_control', '0044_merge_20220729_2007'),
    ]

    operations = [
        migrations.CreateModel(
            name='RequestCommunity',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_requested', models.DateTimeField(auto_now_add=True)),
                ('date_processed', models.DateTimeField(editable=False, null=True)),
                ('pending_approval', models.BooleanField(default=True)),
                ('declined', models.BooleanField(default=False)),
                ('decline_reason', models.TextField(blank=True, null=True)),
            ],
            options={
                'ordering': ['date_requested'],
            },
        ),
        migrations.RemoveField(
            model_name='communityrequest',
            name='owner',
        ),
        migrations.AlterModelOptions(
            name='community',
            options={'ordering': ['date_created']},
        ),
        migrations.RenameField(
            model_name='community',
            old_name='auto_approve',
            new_name='auto_approve_group',
        ),
        migrations.AddField(
            model_name='community',
            name='auto_approve_resource',
            field=models.BooleanField(default=False, editable=False),
        ),
        migrations.AddField(
            model_name='community',
            name='banner',
            field=sorl.thumbnail.fields.ImageField(blank=True, null=True, upload_to=theme.utils.get_upload_path_community),
        ),
        migrations.AlterField(
            model_name='community',
            name='active',
            field=models.BooleanField(default=False, editable=False),
        ),
        migrations.DeleteModel(
            name='CommunityRequest',
        ),
        migrations.AddField(
            model_name='requestcommunity',
            name='community_to_approve',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='c2crequest', to='hs_access_control.Community'),
        ),
        migrations.AddField(
            model_name='requestcommunity',
            name='requested_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='u2crequest', to=settings.AUTH_USER_MODEL),
        ),
    ]
