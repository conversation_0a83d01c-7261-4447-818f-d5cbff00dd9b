# -*- coding: utf-8 -*-
# Generated by Django 1.11.18 on 2019-09-24 16:22


from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('hs_access_control', '0024_remove_groupcommunityprivilege_allow_view'),
    ]

    operations = [
        migrations.CreateModel(
            name='Feature',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feature', models.IntegerField(choices=[(0, b'None'), (1, b'CZO')], default=0)),
                ('enabled', models.BooleanField(default=False)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='feature', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AlterUniqueTogether(
            name='feature',
            unique_together=set([('user', 'feature')]),
        ),
    ]
